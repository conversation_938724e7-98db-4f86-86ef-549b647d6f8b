[build-system]
requires = ["setuptools>=61.0", "wheel", "pybind11"]
build-backend = "setuptools.build_meta"

[project]
name = "color_limiting"
version = "1.0"
description = "Color Limiting Algorithms"
requires-python = ">=3.7"
dependencies = [
    "opencv-python",
    "numpy",
    "pybind11"
]

[tool.setuptools]
packages = ["app"]

[tool.black]
line-length = 88
target-version = ['py37']
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"

[tool.flake8]
max-line-length = 88
exclude = [".git", "__pycache__", "build", "dist", ".tox"]

[tool.mypy]
python_version = "3.7"
ignore_missing_imports = true
strict = false

[tool.commitizen]
name = "cz_conventional_commits"
version = "1.0.0"
tag_format = "v$version"
version_files = [
    "setup.py:version",
    "pyproject.toml:version"
]

[tool.poetry]
name = "color_limiting"
version = "1.0.0"
description = "Color Limiting Algorithms"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "color_limiting"}]

[tool.poetry.dependencies]
python = "^3.7"
opencv-python = "*"
numpy = "*"
pybind11 = "*"

[tool.poetry.group.dev.dependencies]
black = "^23.3"
flake8 = "^6.0"
isort = "^5.12"
mypy = "^1.2"
commitizen = "^3.4"
pre-commit = "^3.3"

[tool.poetry.build]
generate-setup-file = false
