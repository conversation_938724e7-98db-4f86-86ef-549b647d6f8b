---
name: Test and Deploy

on:
  push:
    branches: [dev]
  pull_request:
    branches: [main]
  workflow_dispatch:
  # Manual trigger

env:
  API_ACCESS_KEY: ${{ secrets.API_ACCESS_KEY }}
  MODAL_KEY: ${{ secrets.MODAL_KEY }}
  EDEN_AI_API_KEY_PRODUCTION: ${{ secrets.EDEN_AI_API_KEY_PRODUCTION }}

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      # Cache Python dependencies
      - name: Cache pip packages
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      # Cache Docker layers
      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Build Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          load: true
          tags: mixmix-python-middleware:latest
          cache-from: |
            type=local,src=/tmp/.buildx-cache
            type=gha
          cache-to: |
            type=local,dest=/tmp/.buildx-cache-new,mode=max
            type=gha,mode=max

      # Move cache to avoid growing it indefinitely
      - name: Move cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      # First start the container in the background
      - name: Start container for testing
        run: |
          docker run -d \
            -p 8000:8000 \
            -e API_ACCESS_KEY=${{ secrets.API_ACCESS_KEY }} \
            -e MODAL_KEY=${{ secrets.MODAL_KEY }} \
            -e EDEN_AI_API_KEY_PRODUCTION=${{ secrets.EDEN_AI_API_KEY_PRODUCTION }} \
            --name mixmix-container \
            mixmix-python-middleware:latest

          # Wait for the container to be ready
          sleep 5

          # Verify the container is running
          docker ps | grep mixmix-container

      # Then run tests in a separate step
      - name: Run tests in Docker
        run: |
          # Run tests inside the container
          docker exec mixmix-container pytest -v tests/

      # Cleanup when done
      - name: Cleanup
        if: always()
        run: |
          docker stop mixmix-container
          docker rm mixmix-container
