import image_editing_cpp
from app.core.base_classes import ImageProcessor
from app.utils.func_response import FuncResponse
from app.utils.logging import log_time


class AdjustHueSaturation(ImageProcessor):
    def __init__(self, image_data: bytes, filename: str, hue: float, saturation: float) -> None:
        super().__init__()
        self.image_data = self.get_image_in_bytes(image_data)
        self.hue = hue
        self.saturation = saturation

    @log_time
    def process(self) -> FuncResponse:
        """Process image using PNG format internally for quality preservation.

        Returns:
            FuncResponse: The processed image data in PNG format or error message
        """
        try:
            # Use PNG format internally for quality preservation
            image = image_editing_cpp.adjust_hue_saturation(
                self.image_data, self.hue, self.saturation, "png"
            )
            return FuncResponse.success(data=image, source="AdjustHueSaturation.process()")
        except Exception as e:
            self.logger.error(f"Error processing image: {str(e)}")
            return FuncResponse.failure(
                error_message=f"{str(e)}", source="AdjustHueSaturation.process()"
            )


if __name__ == "__main__":
    input_path = "fixtures/images/input.jpg"
    output_path = "temp" + input_path[input_path.rfind(".") :]

    # Read the file and create an UploadFile object
    with open(input_path, "rb") as img_file:
        image_data = img_file.read()

    processor = AdjustHueSaturation(image_data, input_path, 0.2, 0.2)
    result = processor.process()

    if result.success:
        with open(output_path, "wb") as out_file:
            out_file.write(result.data)
        print("Image processed successfully and saved to", output_path)
    else:
        print(f"Error processing image: {result.error_message}")
