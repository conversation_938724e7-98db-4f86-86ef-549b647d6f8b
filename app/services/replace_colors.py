import pixel_manipulation_cpp
from app.core.base_classes import ImageProcessor
from app.utils.func_response import FuncResponse
from app.utils.logging import log_time


class ReplaceColors(ImageProcessor):
    def __init__(
        self,
        image_data: bytes,
        replacements: dict[str, str],
        texturize: int,
        filename: str,
    ) -> None:
        super().__init__()
        self.image_data = self.get_image_in_bytes(image_data)
        self.replacements = replacements
        self.image_format = self.get_image_format(filename)
        self.texturize = texturize

    @log_time
    def process(self) -> FuncResponse:
        """Process image using PNG format for quality preservation.

        Returns:
            FuncResponse: The processed image data in PNG format or error message
        """
        try:
            # Use PNG format internally for quality preservation during intermediate processing
            image = pixel_manipulation_cpp.replace_colors(
                self.image_data, self.replacements, self.texturize, "png"
            )
            return FuncResponse.success(data=image, source="ReplaceColors.process()")
        except Exception as e:
            self.logger.error(f"Error processing image: {str(e)}")
            return FuncResponse.failure(error_message=f"{str(e)}", source="ReplaceColors.process()")


if __name__ == "__main__":
    input_path = "psd_files/input.png"
    output_path = "temp.png"
    texturize = 1
    color_to_replace = "#002E3D"  # (0, 46, 61)
    new_color = "#000000"
    with open(input_path, "rb") as img_file:
        image_data = img_file.read()

    # Create UploadFile object from image data
    processor = ReplaceColors(image_data, {color_to_replace: new_color}, texturize, "input.png")
    result = processor.process()
    if result.success:
        with open(output_path, "wb") as out_file:
            out_file.write(result.data)
        print("Image processed successfully and saved to", output_path)
    else:
        print(f"Error processing image: {result.error_message}")
