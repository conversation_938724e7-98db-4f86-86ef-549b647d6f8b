import numpy as np
import io
import sys
import os
import abc
from PIL import Image
from app.utils.logging import log_time
from app.utils.image_format_converter import image_format_converter
from typing import Dict, List, Type


__all__ = ["color_limiter"]


class ColorQuantizer(abc.ABC):
    """Abstract base class for all color quantization algorithms."""

    @abc.abstractmethod
    def quantize(self, image_data: bytes, num_colors: int) -> bytes:
        """Quantize an image to a limited color palette.

        Args:
            image_data: The input image as bytes
            num_colors: The number of colors to limit to

        Returns:
            bytes: The quantized image as bytes (format will be converted separately)
        """
        pass

    @property
    @abc.abstractmethod
    def name(self) -> str:
        """Return the name of the algorithm"""
        pass


class MedianCutQuantizer(ColorQuantizer):
    """Median Cut color quantization implementation using C++ extension."""

    def __init__(self):
        import median_cut_cpp

        self.median_cut_cpp = median_cut_cpp

    def quantize(self, image_data: bytes, num_colors: int) -> bytes:
        # Process with median cut, using PNG to preserve quality for intermediate processing
        result = self.median_cut_cpp.medianCutQuantization(image_data, num_colors, "png")

        return result

    @property
    def name(self) -> str:
        return "median_cut"


class KMeansQuantizer(ColorQuantizer):
    """K-means color quantization implementation using C++ extension."""

    def __init__(self):
        import k_means_cpp

        self.k_means_cpp = k_means_cpp

    def quantize(self, image_data: bytes, num_colors: int) -> bytes:
        # Convert bytes to numpy array
        img = Image.open(io.BytesIO(image_data))
        np_image = np.array(img)
        height, width, channels = np_image.shape

        # Process with k-means (returns raw pixel data)
        result_bytes = self.k_means_cpp.limit_image_colors(
            np_image, height, width, channels, num_colors
        )

        # Convert raw bytes back to numpy array
        result_array = np.frombuffer(result_bytes, dtype=np.uint8).reshape(height, width, channels)

        # Convert to PIL Image and return as PNG bytes to preserve quality for intermediate processing
        result_img = Image.fromarray(result_array)
        return image_format_converter.convert_pil_to_format(result_img, "png")

    @property
    def name(self) -> str:
        return "k_means"


class AdaptiveDitheringQuantizer(ColorQuantizer):
    """Adaptive dithering color quantization implementation using C++ extension."""

    def __init__(self):
        import adaptive_dithering_cpp

        self.adaptive_dithering_cpp = adaptive_dithering_cpp

    def quantize(self, image_data: bytes, num_colors: int) -> bytes:
        # Process with adaptive dithering, using PNG to preserve quality for intermediate processing
        result = self.adaptive_dithering_cpp.quantize_dither_image(image_data, num_colors, "png")

        return result

    @property
    def name(self) -> str:
        return "adaptive_dithering"


class CPPColorLimiter:
    """Implementation of ColorLimiter that uses C++ extensions for performance.

    This class consolidates the functionality of QuantizerFactory and CPPColorLimiter
    to avoid duplication.
    """

    # Dictionary of available quantizer instances
    _quantizer_classes: Dict[str, Type[ColorQuantizer]] = {
        "median_cut": MedianCutQuantizer(),
        "k_means": KMeansQuantizer(),
        "adaptive_dithering": AdaptiveDitheringQuantizer(),
    }

    def __init__(self):
        pass

    def register_quantizer_class(self, name: str, quantizer_class: Type[ColorQuantizer]) -> None:
        """Register a new quantizer class.

        Args:
            name: The name of the algorithm
            quantizer_class: The quantizer class (not an instance)
        """
        if not issubclass(quantizer_class, ColorQuantizer):
            raise TypeError("Quantizer class must be a subclass of ColorQuantizer")
        if name in self._quantizer_classes:
            raise ValueError(f"Algorithm {name} is already registered")
        self._quantizer_classes[name] = quantizer_class

    def get_quantizer(self, algorithm_name: str) -> ColorQuantizer:
        """Get a quantizer instance by name (creating it if necessary).

        Args:
            algorithm_name: The name of the algorithm

        Returns:
            An instance of the requested quantizer

        Raises:
            ValueError: If the algorithm is not registered
            ImportError: If the required C++ module is not available
        """
        if algorithm_name not in self._quantizer_classes:
            raise ValueError(
                f"Unknown algorithm: {algorithm_name}. Available algorithms: "
                f"{', '.join(self.get_available_algorithms())}"
            )

        return self._quantizer_classes[algorithm_name]

    @log_time
    def limit_colors(
        self, image_data: bytes, num_colors: int = 12, algorithm: str = "median_cut"
    ) -> bytes:
        """Limit the colors in the image using the specified algorithm.

        Args:
            image_data: The image data as bytes
            num_colors: The number of colors to limit to
            algorithm: The algorithm to use

        Returns:
            bytes: The quantized image as bytes (format will be converted separately)

        Raises:
            ValueError: If the algorithm is not supported
            ImportError: If the required C++ module is not available
            Exception: If the quantization fails
        """
        quantizer = self.get_quantizer(algorithm)
        return quantizer.quantize(image_data, num_colors)

    def get_available_algorithms(self) -> List[str]:
        """Get a list of all registered algorithm names."""
        return list(self._quantizer_classes.keys())


@log_time
def save_image(image_data, output_path):
    """Save image bytes to a file."""
    with open(output_path, "wb") as f:
        f.write(image_data)
        print(f"Image saved to {output_path}")


color_limiter = CPPColorLimiter()

if __name__ == "__main__":
    # Handle command-line arguments
    if len(sys.argv) == 2 and sys.argv[1] == "--list-algorithms":
        print("Available algorithms:")
        for algo in color_limiter.get_available_algorithms():
            print(f"  - {algo}")
        sys.exit(0)

    if len(sys.argv) not in [3, 4]:
        print(
            "Usage: python cpp_color_limiting.py <image_path> <num_colors> [algorithm]\n"
            "       python cpp_color_limiting.py --list-algorithms"
        )
        sys.exit(1)

    image_path = sys.argv[1]
    num_colors = int(sys.argv[2])
    algorithm = sys.argv[3] if len(sys.argv) == 4 else "median_cut_cpp"

    # Process the image
    try:
        file_name = os.path.basename(image_path).split(".")[0]
        output_dir = "psd_files/png/limited"
        os.makedirs(output_dir, exist_ok=True)
        output_path = f"{output_dir}/{file_name}_{algorithm}_{num_colors}.png"

        # Read input image
        with open(image_path, "rb") as f:
            image_data = f.read()

        # Process the image
        result_bytes = color_limiter.limit_colors(image_data, num_colors, algorithm)

        save_image(result_bytes, output_path)

    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)
