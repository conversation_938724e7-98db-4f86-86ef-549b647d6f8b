import image_editing_cpp
from app.core.base_classes import ImageProcessor
from app.utils.func_response import FuncResponse
from app.utils.logging import log_time


class ConvertToGrayscale(ImageProcessor):
    def __init__(self, image_data: bytes, filename: str) -> None:
        super().__init__()
        self.image_data = self.get_image_in_bytes(image_data)

    @log_time
    def process(self) -> FuncResponse:
        """Process image using PNG format internally for quality preservation.

        Returns:
            FuncResponse: The processed image data in PNG format or error message
        """
        try:
            # Use PNG format internally for quality preservation
            image = image_editing_cpp.convert_to_grayscale(self.image_data, "png")
            return FuncResponse.success(data=image, source="ConvertToGrayscale.process()")
        except Exception as e:
            self.logger.error(f"Error processing image: {str(e)}")
            return FuncResponse.failure(
                error_message=f"{str(e)}", source="ConvertToGrayscale.process()"
            )


if __name__ == "__main__":
    input_path = "fixtures/images/input.jpg"
    output_path = "temp" + input_path[input_path.rfind(".") :]
    with open(input_path, "rb") as img_file:
        image_data = img_file.read()

    # Create UploadFile object from image data
    processor = ConvertToGrayscale(image_data, input_path[input_path.rfind("/") + 1 :])
    result = processor.process()
    if result.success:
        with open(output_path, "wb") as out_file:
            out_file.write(result.data)
        print("Image processed successfully and saved to", output_path)
    else:
        print(f"Error processing image: {result.error_message}")
