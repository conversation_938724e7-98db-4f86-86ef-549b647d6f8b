import scale_image_cpp
from app.core.base_classes import ImageProcessor
from app.utils.func_response import FuncResponse
from app.utils.logging import log_time


class ScaleImage(ImageProcessor):
    def __init__(self, image_data: bytes, filename: str, scale_factor: float = 1.0) -> None:
        super().__init__()
        self.image_data = self.get_image_in_bytes(image_data)
        self.scale_factor = scale_factor

    @log_time
    def process(self) -> FuncResponse:
        """Process image using PNG format internally for quality preservation.

        Returns:
            FuncResponse: The processed image data in PNG format or error message
        """
        try:
            # Use PNG format internally for quality preservation
            scaled_image = scale_image_cpp.scale_image(self.image_data, self.scale_factor, "png")
            return FuncResponse.success(data=scaled_image, source="ScaleImage.process()")
        except Exception as e:
            self.logger.error(f"Error processing image: {str(e)}")
            return FuncResponse.failure(error_message=f"{str(e)}", source="ScaleImage.process()")


if __name__ == "__main__":
    input_path = "psd_files/input.png"
    output_path = "temp" + input_path[input_path.rfind(".") :]
    with open(input_path, "rb") as img_file:
        image_data = img_file.read()

    # Create UploadFile object from image data
    processor = ScaleImage(image_data, "input.jpg", 0.6)  # Example with scale factor
    result = processor.process()
    if result.success:
        # Save the result
        with open(output_path, "wb") as out_file:
            out_file.write(result.data)
        print(f"Scaled image saved to {output_path}")
    else:
        print(f"Error processing image: {result.error_message}")
