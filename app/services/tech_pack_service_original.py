import io
import logging
from typing import <PERSON><PERSON>, <PERSON><PERSON>, List
from collections import Counter
from dataclasses import dataclass
from PIL import Image
from reportlab.pdfgen import canvas

logger = logging.getLogger(__name__)

PDF_CM = 28.3465  # 1 cm = 28.3465 points


@dataclass
class ArtworkInfo:
    """Data class containing artwork information."""

    filename: str
    width_px: int
    height_px: int
    width_inches: float
    height_inches: float
    hex_colors: List[str]


@dataclass
class InfoBlockLayout:
    """Data class containing layout for the information block."""

    row_top_margin: float = 0.4 * PDF_CM
    row_left_margin: float = 0.5 * PDF_CM
    font_size: int = 18
    info_block_height: float = (row_top_margin * 2) + (font_size * 2)
    brand_logo: Image.Image = None
    max_logo_width: float = 4 * PDF_CM
    max_logo_heigh: float = info_block_height - row_left_margin


@dataclass
class ArrowLayout:
    """Data class containing layout for the arrows."""

    arrow_head_length: float = 0.6 * PDF_CM
    arrow_head_width: float = PDF_CM
    font_size: float = 15
    arrow_artwork_offset: float = 1.75 * PDF_CM
    selvedge_text_gap: int = 150
    top_text_gap: int = 100
    text_dip: float = 5


@dataclass
class SwatchBlockLayout:
    """Data class containing layout for color swatch blocks."""

    swatches_per_row: int = 12
    swatch_width: float = 2 * PDF_CM
    swatch_height: float = PDF_CM
    swatch_intra_row_gap: float = 0.23 * PDF_CM
    swatch_row_left_margin: float = 0.5 * PDF_CM
    swatch_text_margin: float = 0.4 * PDF_CM
    swatch_row_top_margin: float = None
    color_font_size: int = 12
    total_swatch_rows: int = None
    swatch_block_height: float = None


@dataclass
class PDFLayout:
    """Data class containing PDF layout calculations."""

    artwork_height: float = None
    artwork_width: float = None
    total_page_height: float = None
    artwork_margin: float = 3.5 * PDF_CM
    info_swatch_margin: float = 0.52 * PDF_CM
    info_block = InfoBlockLayout()
    swatch_block = SwatchBlockLayout()
    arrows = ArrowLayout()


class ImageProcessor:
    """Handles image processing operations."""

    def __init__(self, dpi: int = 300):
        self.dpi = dpi

    def calculate_size_in_inches(
        self, width_px: int, height_px: int
    ) -> Tuple[float, float]:
        """Calculate image size in inches based on DPI."""
        width_inches = width_px / self.dpi
        height_inches = height_px / self.dpi
        return round(width_inches, 2), round(height_inches, 2)


class ColorExtractor:
    """Handles color extraction from images."""

    def extract_hex_colors(self, image_bytes: bytes, max_colors: int = 20) -> List[str]:
        """Extract the most common hex colors from the image using optimized sampling."""
        try:
            image = Image.open(io.BytesIO(image_bytes))

            # Convert to RGB if necessary
            if image.mode != "RGB":
                image = image.convert("RGB")

            # Optimized resizing for color extraction
            sample_size = 100
            if image.width > sample_size or image.height > sample_size:
                ratio = min(sample_size / image.width, sample_size / image.height)
                new_width = int(image.width * ratio)
                new_height = int(image.height * ratio)
                image = image.resize((new_width, new_height), Image.Resampling.NEAREST)

            # Use numpy for faster pixel processing
            try:
                import numpy as np

                pixels_array = np.array(image)
                pixels = pixels_array.reshape(-1, 3)
                pixel_tuples = [tuple(pixel) for pixel in pixels[::16]]
            except ImportError:
                pixels = list(image.getdata())
                pixel_tuples = pixels[::16]

            # Count color frequencies
            color_counts = Counter(pixel_tuples)

            # Convert to hex
            hex_colors = []
            for (r, g, b), count in color_counts.most_common(max_colors):
                hex_color = f"#{r:02X}{g:02X}{b:02X}"
                hex_colors.append(hex_color)

            return hex_colors

        except Exception as e:
            logger.error(f"Error extracting colors: {str(e)}")
            return ["#000000"]


class PDFLayoutCalculator:
    """Calculates PDF layout dimensions."""

    def __init__(self, dpi: int = 300):
        self.dpi = dpi

    def calculate_layout(self, artwork_info: ArtworkInfo) -> PDFLayout:
        """Calculate PDF layout dimensions based on artwork info."""
        pdf_block = PDFLayout
        pdf_block.artwork_width = artwork_info.width_px * 72 / self.dpi
        pdf_block.artwork_height = artwork_info.height_px * 72 / self.dpi

        pdf_block.swatch_block.total_swatch_rows = (
            len(artwork_info.hex_colors) + pdf_block.swatch_block.swatches_per_row - 1
        ) // pdf_block.swatch_block.swatches_per_row

        if pdf_block.swatch_block.total_swatch_rows == 1:
            m = 0
        elif pdf_block.swatch_block.total_swatch_rows == 2:
            m = 2.75 * PDF_CM
        else:
            m = 1.53 * PDF_CM
        pdf_block.swatch_block.swatch_row_top_margin = m

        pdf_block.swatch_block.swatch_block_height = (
            pdf_block.swatch_block.total_swatch_rows
            * (
                pdf_block.swatch_block.swatch_height
                + pdf_block.swatch_block.swatch_row_top_margin
            )
        )

        pdf_block.total_page_height = (
            pdf_block.artwork_height
            + pdf_block.swatch_block.swatch_block_height
            + pdf_block.info_block.info_block_height
            + 2 * pdf_block.artwork_margin
        )

        return pdf_block


class ArtworkInfoRenderer:
    """Renders artwork information section in PDF."""

    def __init__(self, default_font: str = "Helvetica"):
        self.default_font = default_font

    def render(
        self,
        canvas_obj: canvas.Canvas,
        artwork_info: ArtworkInfo,
        layout: PDFLayout,
        start_x: float,
        start_y: float,
    ):
        """Render artwork information at specified position."""
        canvas_obj.setFont(self.default_font, layout.info_block.font_size)

        # FILE NAME
        text = canvas_obj.beginText()
        text.setTextOrigin(
            start_x + layout.swatch_block.swatch_row_left_margin,
            start_y - layout.info_block.row_top_margin,
        )
        # Bold filename label
        text.setFont(self.default_font + "-Bold", 12)
        text.textOut("FILE NAME: ")
        # Regular filename
        text.setFont(self.default_font, 12)
        text.textOut(artwork_info.filename)
        canvas_obj.drawText(text)

        # REPEAT TILE SIZE
        text = f'REPEAT TILE SIZE: {artwork_info.width_inches}" WIDE x {artwork_info.height_inches}" LONG'
        canvas_obj.drawString(
            start_x + layout.info_block.row_left_margin,
            start_y - layout.info_block.info_block_height + layout.info_block.font_size,
            text,
        )


class ColorSwatchRenderer:
    """Renders color swatches in PDF."""

    def __init__(self, default_font: str = "Helvetica"):
        self.default_font = default_font

    def render(
        self,
        canvas_obj: canvas.Canvas,
        artwork_info: ArtworkInfo,
        layout: PDFLayout,
        start_x: float,
        start_y: float,
    ):
        """Render color swatches at specified position."""
        s = layout.swatch_block
        for idx, hex_color in enumerate(artwork_info.hex_colors):
            row = idx // s.swatches_per_row
            col = idx % s.swatches_per_row
            x = (
                start_x
                + s.swatch_row_left_margin
                + col * (s.swatch_width + s.swatch_intra_row_gap)
            )
            y = start_y - row * (s.swatch_height + s.swatch_row_top_margin)

            # Draw color swatch
            canvas_obj.setFillColor(hex_color)
            canvas_obj.rect(x, y, s.swatch_width, s.swatch_height, fill=1, stroke=0)

            # Draw hex code below swatch with added margin
            canvas_obj.setFillColor("black")
            canvas_obj.setFont(self.default_font, s.color_font_size)
            canvas_obj.drawString(x, y - s.swatch_text_margin, hex_color)


class ArrowRenderer:
    """Renders arrows and labels around artwork."""

    def __init__(self, default_font: str = "Helvetica"):
        self.default_font = default_font

    def draw_arrow_head(
        self,
        canvas_obj: canvas.Canvas,
        x: float,
        y: float,
        direction: str,
        arrow_layout: ArrowLayout,
    ):
        """Draw a single arrow head pointing in the specified direction."""

        length = arrow_layout.arrow_head_length
        width = arrow_layout.arrow_head_width

        if direction == "up":
            points = [x, y + length, x - width / 2, y, x + width / 2, y]
        elif direction == "down":
            points = [x, y - length, x - width / 2, y, x + width / 2, y]
        elif direction == "left":
            points = [x - length, y, x, y - width / 2, x, y + width / 2]
        elif direction == "right":
            points = [x + length, y, x, y - width / 2, x, y + width / 2]

        path = canvas_obj.beginPath()
        path.moveTo(points[0], points[1])
        path.lineTo(points[2], points[3])
        path.lineTo(points[4], points[5])
        path.close()
        canvas_obj.drawPath(path, fill=1)

    def draw_double_headed_arrow(
        self,
        canvas_obj: canvas.Canvas,
        x1: float,
        y1: float,
        x2: float,
        y2: float,
        arrow_layout: ArrowLayout,
        is_vertical: bool = False,
    ):
        """Draw a double-headed arrow between two points with text inside."""
        canvas_obj.setStrokeColor("black")
        canvas_obj.setLineWidth(1)

        # Calculate text position and create gap for text
        if is_vertical:
            # For vertical arrows (SELVEDGE)
            mid_y = (y1 + y2) / 2
            gap_size = arrow_layout.selvedge_text_gap

            # Draw line with gap in middle
            canvas_obj.line(x1, y1, x1, mid_y - gap_size / 2)  # Top half
            canvas_obj.line(x1, mid_y + gap_size / 2, x1, y2)  # Bottom half

            # Fix arrow head directions: they should point towards each other
            self.draw_arrow_head(
                canvas_obj, x1, y1, "down", arrow_layout
            )  # Top arrow head points down
            self.draw_arrow_head(
                canvas_obj, x1, y2, "up", arrow_layout
            )  # Bottom arrow head points up
        else:
            # For horizontal arrow (TOP)
            mid_x = (x1 + x2) / 2
            gap_size = arrow_layout.top_text_gap

            # Draw line with gap in middle
            canvas_obj.line(x1, y1, mid_x - gap_size / 2, y1)  # Left half
            canvas_obj.line(mid_x + gap_size / 2, y1, x2, y1)  # Right half

            # Draw arrow heads
            self.draw_arrow_head(canvas_obj, x1, y1, "left", arrow_layout)
            self.draw_arrow_head(canvas_obj, x2, y2, "right", arrow_layout)

    def render_selvedge_arrows(
        self,
        canvas_obj: canvas.Canvas,
        artwork_info: ArtworkInfo,
        layout: PDFLayout,
        img_x: float,
        img_y: float,
        arrow_layout: ArrowLayout,
    ):
        """Render SELVEDGE arrows on both sides of the artwork."""

        # Position arrows at the edge of the image, with arrow heads inside the image boundaries
        # Left side arrow - position it just inside the left edge
        left_arrow_x = img_x - arrow_layout.arrow_artwork_offset
        left_arrow_y1 = (
            img_y + arrow_layout.arrow_head_length
        )  # Start arrow heads inside image
        left_arrow_y2 = (
            img_y + layout.artwork_height - arrow_layout.arrow_head_length
        )  # End arrow heads inside image

        # Right side arrow - position it just inside the right edge
        right_arrow_x = img_x + layout.artwork_width + arrow_layout.arrow_artwork_offset
        right_arrow_y1 = (
            img_y + arrow_layout.arrow_head_length
        )  # Start arrow heads inside image
        right_arrow_y2 = (
            img_y + layout.artwork_height - arrow_layout.arrow_head_length
        )  # End arrow heads inside image

        # Draw vertical arrows on both sides
        self.draw_double_headed_arrow(
            canvas_obj,
            left_arrow_x,
            left_arrow_y1,
            left_arrow_x,
            left_arrow_y2,
            layout.arrows,
            is_vertical=True,
        )
        self.draw_double_headed_arrow(
            canvas_obj,
            right_arrow_x,
            right_arrow_y1,
            right_arrow_x,
            right_arrow_y2,
            layout.arrows,
            is_vertical=True,
        )

        # Add SELVEDGE text in the middle of arrows (inside the gap)
        canvas_obj.setFont(self.default_font, layout.arrows.font_size)

        # Left side text (rotated 90 degrees clockwise) - positioned in middle of arrow
        left_text_x = left_arrow_x + arrow_layout.text_dip
        left_text_y = img_y + layout.artwork_height / 2
        canvas_obj.saveState()
        canvas_obj.translate(left_text_x, left_text_y)
        canvas_obj.rotate(90)
        canvas_obj.drawCentredString(0, 0, f"SELVEDGE {artwork_info.height_inches}'' h")
        canvas_obj.restoreState()

        # Right side text (rotated 90 degrees counter-clockwise) - positioned in middle of arrow
        right_text_x = right_arrow_x - arrow_layout.text_dip
        right_text_y = img_y + layout.artwork_height / 2
        canvas_obj.saveState()
        canvas_obj.translate(right_text_x, right_text_y)
        canvas_obj.rotate(-90)
        canvas_obj.drawCentredString(0, 0, f"SELVEDGE {artwork_info.height_inches}'' h")
        canvas_obj.restoreState()

    def render_top_arrow(
        self,
        canvas_obj: canvas.Canvas,
        artwork_info: ArtworkInfo,
        layout: PDFLayout,
        img_x: float,
        img_y: float,
        arrow_layout: ArrowLayout,
    ):
        """Render TOP arrow above the artwork."""

        # Top arrow - position arrow heads inside the image boundaries
        top_arrow_y = img_y + layout.artwork_height + arrow_layout.arrow_artwork_offset
        top_arrow_x1 = (
            img_x + arrow_layout.arrow_head_length
        )  # Start arrow heads inside image
        top_arrow_x2 = (
            img_x + layout.artwork_width - arrow_layout.arrow_head_length
        )  # End arrow heads inside image

        # Draw horizontal arrow above artwork
        self.draw_double_headed_arrow(
            canvas_obj,
            top_arrow_x1,
            top_arrow_y,
            top_arrow_x2,
            top_arrow_y,
            arrow_layout,
            is_vertical=False,
        )

        # Add TOP text in the middle of the arrow (inside the gap)
        canvas_obj.setFont(self.default_font, layout.arrows.font_size)
        top_text_x = img_x + layout.artwork_width / 2  # Center of the full image width
        top_text_y = top_arrow_y - arrow_layout.text_dip  # Position text slightly below the arrow line
        canvas_obj.drawCentredString(
            top_text_x, top_text_y, f"TOP {artwork_info.width_inches}'' w"
        )

    def render(
        self,
        canvas_obj: canvas.Canvas,
        artwork_info: ArtworkInfo,
        layout: PDFLayout,
        img_x: float,
        img_y: float,
        arrow_layout: ArrowLayout,
    ):
        """Render all arrows and labels around the artwork."""
        canvas_obj.setFillColor("black")
        self.render_selvedge_arrows(
            canvas_obj, artwork_info, layout, img_x, img_y, arrow_layout
        )
        self.render_top_arrow(
            canvas_obj, artwork_info, layout, img_x, img_y, arrow_layout
        )


class ArtworkPDFGenerator:
    """Orchestrates PDF generation for artwork."""

    def __init__(self, dpi: int = 300, default_font: str = "Helvetica"):
        self.image_processor = ImageProcessor(dpi)
        self.color_extractor = ColorExtractor()
        self.layout_calculator = PDFLayoutCalculator(dpi=dpi)
        self.info_renderer = ArtworkInfoRenderer(default_font)
        self.swatch_renderer = ColorSwatchRenderer(default_font)
        self.arrow_renderer = ArrowRenderer(default_font)

    def generate_artwork_info(
        self,
        image_bytes: bytes,
        filename: str,
    ) -> ArtworkInfo:
        """Generate artwork information from image bytes."""
        image = Image.open(io.BytesIO(image_bytes))
        img_width, img_height = image.size
        width_inches, height_inches = self.image_processor.calculate_size_in_inches(
            img_width, img_height
        )
        hex_colors = self.color_extractor.extract_hex_colors(image_bytes)

        return ArtworkInfo(
            filename=filename,
            width_px=img_width,
            height_px=img_height,
            width_inches=width_inches,
            height_inches=height_inches,
            hex_colors=hex_colors,
        )

    def generate_pdf(
        self, artwork_info: ArtworkInfo, image: Image.Image, output_path: str = None
    ) -> bytes:
        """Generate PDF with artwork info and color swatches."""
        layout = self.layout_calculator.calculate_layout(artwork_info)

        c = canvas.Canvas(
            output_path or io.BytesIO(),
            pagesize=(
                layout.artwork_width + 2 * layout.artwork_margin,
                layout.total_page_height,
            ),
        )

        # Draw image at top
        img_x = layout.artwork_margin
        img_y = layout.total_page_height - layout.artwork_margin - layout.artwork_height
        c.drawInlineImage(
            image,
            img_x,
            img_y,
            width=layout.artwork_width,
            height=layout.artwork_height,
        )

        # Draw artwork info below image
        info_y = img_y - layout.info_block.row_top_margin
        info_x = layout.artwork_margin
        self.info_renderer.render(c, artwork_info, layout, info_x, info_y)

        # Draw color swatches below info
        swatch_start_y = (
            info_y - layout.info_block.info_block_height - layout.info_swatch_margin
        )
        swatch_start_x = layout.artwork_margin
        self.swatch_renderer.render(
            c, artwork_info, layout, swatch_start_x, swatch_start_y
        )

        # Draw arrows around artwork
        self.arrow_renderer.render(c, artwork_info, layout, img_x, img_y, layout.arrows)

        c.save()
        if output_path:
            return None
        else:
            return c.getpdfdata()


class TechPackService:
    """Service for creating tech pack zip files with PSD, PDF with artwork info, and cropped PDF."""

    def __init__(self):
        self.default_font_size = 12
        self.default_font = "Helvetica"
        self.default_pdf_inches = 30
        self.dpi = 300
        self.pdf_generator = ArtworkPDFGenerator(self.dpi, self.default_font)

    def generate_artwork_pdf(
        self,
        image_bytes: bytes,
        filename: str,
        output_path: str = None,
    ) -> bytes:
        """Generate a PDF with artwork info and color swatches, no quality loss."""
        # Generate artwork information
        artwork_info = self.pdf_generator.generate_artwork_info(image_bytes, filename)
        image = Image.open(io.BytesIO(image_bytes))
        return self.pdf_generator.generate_pdf(artwork_info, image, output_path)


if __name__ == "__main__":
    # Example usage
    service = TechPackService()
    with open("psd_files/input.png", "rb") as f:
        image_bytes = f.read()

    pdf_output_path = "test_input.pdf"
    pdf_output_path_2 = "test_input_11x17.pdf"
    service.generate_artwork_pdf(image_bytes, "TEST!", pdf_output_path)
