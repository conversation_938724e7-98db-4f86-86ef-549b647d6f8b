import tempfile
import json
import requests
import os
from PIL import Image
from app.utils.logging import log_time
from app.utils.file_utils import save_img, save_file
from dotenv import load_dotenv
import logging

load_dotenv()


@log_time
def get_cloth_from_modal(cloth_path, api_key):
    """Detects clothing in an image using the Modal API.

    Args:
        cloth_path (str): Path to the image file.
        api_key (str): API key for the Modal API.

    Returns:
        bytes: The image returned by the Modal API, or -1 if the API call fails.

    Raises:
        FileNotFoundError: If the image file doesn't exist
        IOError: If there's an error reading the file
    """
    MODAL_URL = "https://developer--cloth-detection-objectdetection-run-analysis.modal.run"
    headers = {"Authorization": f"Bearer {api_key}"}

    # FIXME - Return better response structure
    try:
        with open(cloth_path, "rb") as file:
            files = {"file": file}
            response = requests.post(MODAL_URL, headers=headers, files=files)

        if response.status_code != 200:
            logging.error("Error connecting to the API: {response.status_code} {response.text}")
            return {"error": "API call failed", "status_code": response.status_code}

        return {"success": True, "image": response.content}
    except FileNotFoundError:
        logging.error(f"File not found: {cloth_path}")
        return {"success": False, "error": "File not found"}
    except IOError as e:
        logging.error(f"Error reading file: {e}")
        return {"success": False, "error": str(e)}


@log_time
def get_similar_images_from_Eden(cloth_path, api_key, num_results):
    """Searches for similar images using the Eden AI API.

    Sends an image to the Eden AI Similarity Search API and returns the results.

    Args:
        cloth_path (str): Path to the image file.
        api_key (str): API key for the Eden AI API.
        num_results (int): Number of similar images to return.

    Returns:
        dict: A dictionary containing the search results.
    """

    Eden_AI_URL = "https://api.edenai.run/v2/image/search/launch_similarity"

    headers = {"Authorization": f"Bearer {api_key}"}
    data = {"providers": "nyckel", "n": num_results}
    with open(cloth_path, "rb") as file:
        files = {"file": file}
        try:
            response = requests.post(Eden_AI_URL, headers=headers, data=data, files=files)
            response.raise_for_status()  # Raises an HTTPError for bad responses
            result = json.loads(response.text)
            return {"success": True, **result}
        except requests.exceptions.RequestException as e:
            logging.error("Error connecting to the API: {e}")
            return {"success": False, "error": str(e)}
        except json.JSONDecodeError as e:
            logging.error("Error decoding JSON response: {e}")
            return {"success": False, "error": str(e)}


@log_time
def find_similar_images(image, num_results):
    """Find similar images using Modal and Eden AI

    Args:
        image: image data in bytes that is read using PIL.
        num_results: the number of similar images that you want to retrieve from the API.

    Returns:
        message: the response from the API
    """
    if num_results <= 0:
        return [{"error": "Number of results should be greater than 0"}]

    with tempfile.NamedTemporaryFile(suffix=".jpg", delete=True) as temp_file:
        cloth_path = temp_file.name
        save_img(image, cloth_path)

        # Get the cloth image from Modal
        modal_key = os.getenv("MODAL_KEY")
        message = get_cloth_from_modal(cloth_path, modal_key)
        print(message)
        if not message["success"]:
            logging.error(
                "Error retrieving similar images from Eden AI: %s",
                message.get("error", "Unknown error"),
            )
            return [{"error": "Image could not be analyzed"}]
        save_file(message["image"], cloth_path)

        # Get similar images from Eden AI
        api_key = os.getenv("EDEN_AI_API_KEY_PRODUCTION")
        message = get_similar_images_from_Eden(cloth_path, api_key, num_results)
        if not message["success"]:
            logging.error(
                "Error retrieving similar images from Eden AI:",
                message.get("error", "Unknown error"),
            )
            return [message]

        logging.info("similar images found")
        return (
            message["nyckel"]["items"]
            if message.get("nyckel")
            else [{"error": "Runtime error from Eden AI"}]
        )


if __name__ == "__main__":
    file_name = "app/images/test_7.jpg"
    image_data = Image.open(file_name)
    results = find_similar_images(image_data, 20)
    print(results)
