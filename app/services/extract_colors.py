import pixel_manipulation_cpp
from app.core.base_classes import ImageProcessor
from app.utils.func_response import FuncResponse
from app.utils.logging import log_time
from PIL import Image
import io


class ExtractColors(ImageProcessor):
    def __init__(self, image_data: bytes, filename: str) -> None:
        super().__init__()
        self.image_data = self.get_image_in_bytes(image_data)
        self.image_format = self.get_image_format(filename)

    @log_time
    def process(self) -> FuncResponse:
        """Process image maintaining original format.

        Returns:
            FuncResponse: The processed image data or error message
        """
        try:
            list_of_colors = self.extract_colors(self.image_data)
            return FuncResponse.success(data=list_of_colors, source="ExtractColors.process()")
        except Exception as e:
            self.logger.error(f"Error processing image: {str(e)}")
            return FuncResponse.failure(error_message=f"{str(e)}", source="ExtractColors.process()")

    def extract_colors(self, image_data: bytes) -> list[str]:
        """Extracts all colors in an image.

        Args:
            image_data (bytes): The image data to process

        Returns:
            list[str]: A list of colors in hex code found in the image
        """
        try:
            img = Image.open(io.BytesIO(image_data))
            if img.mode != "RGB":
                img = img.convert("RGB")

            colors = img.getcolors(maxcolors=16777216)  # 256^3 for RGB images
            if not colors:
                raise ValueError("Could not count colors in image")

            # Convert the RGB colors to hex format
            hex_colors = [f"#{r:02x}{g:02x}{b:02x}" for _, (r, g, b) in colors]
            self.logger.info(f"Extracted {len(hex_colors)} unique colors from image")
            return hex_colors

        except Exception as e:
            self.logger.error(f"Error extracting colors with PIL: {str(e)}")
            # Fall back to C++ implementation if PIL fails
            return pixel_manipulation_cpp.extract_colors(image_data)


if __name__ == "__main__":
    input_path = "psd_files/png/MMFS077.png"
    output_path = "temp" + input_path[input_path.rfind(".") :]
    with open(input_path, "rb") as img_file:
        image_data = img_file.read()

    # Create UploadFile object from image data
    processor = ExtractColors(image_data, "input.jpg")
    result = processor.process()
    if result.success:
        # print(result.data)
        print(len(result.data), "colors extracted from the image")
    else:
        print(f"Error processing image: {result.error_message}")
