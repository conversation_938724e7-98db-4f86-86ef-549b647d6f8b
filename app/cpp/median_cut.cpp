/**
 * @file median_cut.cpp
 * @brief Median cut color quantization algorithm implementation.
 *
 * Dependencies:
 * - OpenCV (opencv2/opencv.hpp)
 * - STL containers and algorithms
 * - pybind11 for Python integration
 *
 * @warning The number of colors requested must be positive and cannot exceed
 *          the number of unique colors in the input image.
 *
 * <AUTHOR> Agrawal
 * @date 2025-03
 * @version 1.1
 */

#include <omp.h>
#include <pybind11/pybind11.h>

#include <algorithm>
#include <execution>
#include <opencv2/opencv.hpp>
#include <set>
#include <string>
#include <vector>

namespace py = pybind11;

// Custom comparator for cv::Vec3b
struct Vec3bComparator
{
  bool operator()(const cv::Vec3b& a, const cv::Vec3b& b) const
  {
    if (a[0] != b[0]) return a[0] < b[0];
    if (a[1] != b[1]) return a[1] < b[1];
    return a[2] < b[2];
  }
};

struct ColorBox
{
  std::vector<cv::Vec3b> pixels;
  cv::Vec3b              average;
};

cv::Vec3b calculateAverage(const std::vector<cv::Vec3b>& pixels)
{
  // Change from long to double for accumulation
  double sum_b = 0.0, sum_g = 0.0, sum_r = 0.0;

  for (const auto& pixel : pixels)
  {
    sum_b += pixel[0];
    sum_g += pixel[1];
    sum_r += pixel[2];
  }

  int size = pixels.size();
  return cv::Vec3b(sum_b / size, sum_g / size, sum_r / size);
}

int findLargestColorRange(const std::vector<cv::Vec3b>& pixels)
{
  int min_r = 255, min_g = 255, min_b = 255;
  int max_r = 0, max_g = 0, max_b = 0;

  for (const auto& pixel : pixels)
  {
    min_b = std::min(min_b, (int)pixel[0]);
    max_b = std::max(max_b, (int)pixel[0]);
    min_g = std::min(min_g, (int)pixel[1]);
    max_g = std::max(max_g, (int)pixel[1]);
    min_r = std::min(min_r, (int)pixel[2]);
    max_r = std::max(max_r, (int)pixel[2]);
  }

  int range_b = max_b - min_b;
  int range_g = max_g - min_g;
  int range_r = max_r - min_r;

  if (range_b >= range_g && range_b >= range_r) return 0;
  if (range_g >= range_b && range_g >= range_r) return 1;
  return 2;
}

void processInitialBox(const cv::Mat& image, std::vector<ColorBox>& boxes)
{
  ColorBox initialBox;
  for (int i = 0; i < image.rows; i++)
  {
    for (int j = 0; j < image.cols; j++)
    {
      initialBox.pixels.push_back(image.at<cv::Vec3b>(i, j));
    }
  }
  initialBox.average = calculateAverage(initialBox.pixels);
  boxes.push_back(initialBox);
}

void splitBoxes(std::vector<ColorBox>& boxes, int numColors)
{
  int iterations = 0;
  boxes.reserve(numColors);

  while (boxes.size() < numColors)
  {
    iterations++;

    // Find box with largest range
    int boxToSplit = 0;
    int maxRange   = -1;

#pragma omp parallel for reduction(max : maxRange)
    for (size_t i = 0; i < boxes.size(); i++)
    {
      const auto& pixels = boxes[i].pixels;
      int         min_b = 255, min_g = 255, min_r = 255;
      int         max_b = 0, max_g = 0, max_r = 0;

      for (const auto& pixel : pixels)
      {
        min_b = std::min(min_b, (int)pixel[0]);
        max_b = std::max(max_b, (int)pixel[0]);
        min_g = std::min(min_g, (int)pixel[1]);
        max_g = std::max(max_g, (int)pixel[1]);
        min_r = std::min(min_r, (int)pixel[2]);
        max_r = std::max(max_r, (int)pixel[2]);
      }

      int range_b   = max_b - min_b;
      int range_g   = max_g - min_g;
      int range_r   = max_r - min_r;
      int max_range = std::max({range_b, range_g, range_r});

      if (max_range > maxRange)
      {
        maxRange   = max_range;
        boxToSplit = i;
      }
    }

    // Split the box
    int channelToSort = findLargestColorRange(boxes[boxToSplit].pixels);
    std::sort(std::execution::par_unseq,
              boxes[boxToSplit].pixels.begin(),
              boxes[boxToSplit].pixels.end(),
              [channelToSort](const cv::Vec3b& a, const cv::Vec3b& b)
              { return a[channelToSort] < b[channelToSort]; });

    ColorBox box1, box2;
    int      medianIndex = boxes[boxToSplit].pixels.size() / 2;

    box1.pixels.resize(medianIndex);
    box2.pixels.resize(boxes[boxToSplit].pixels.size() - medianIndex);

#pragma omp parallel sections
    {
#pragma omp section
      {
        std::memcpy(
            box1.pixels.data(), boxes[boxToSplit].pixels.data(), medianIndex * sizeof(cv::Vec3b));
        box1.average = calculateAverage(box1.pixels);
      }
#pragma omp section
      {
        std::memcpy(box2.pixels.data(),
                    boxes[boxToSplit].pixels.data() + medianIndex,
                    (boxes[boxToSplit].pixels.size() - medianIndex) * sizeof(cv::Vec3b));
        box2.average = calculateAverage(box2.pixels);
      }
    }

    boxes[boxToSplit] = std::move(box1);
    boxes.push_back(std::move(box2));
  }
}

void createLookupTableAndMapColors(cv::Mat& image, const std::vector<ColorBox>& boxes)
{
  std::vector<cv::Vec3b> palette;
  for (const auto& box : boxes) palette.push_back(box.average);

  std::vector<cv::Vec3b> lookupTable(256 * 256 * 256);
#pragma omp parallel for collapse(3)
  for (int r = 0; r < 256; r++)
  {
    for (int g = 0; g < 256; g++)
    {
      for (int b = 0; b < 256; b++)
      {
        cv::Vec3b pixel(b, g, r);
        cv::Vec3b closest     = palette[0];
        double    minDistance = std::numeric_limits<double>::max();

        for (const auto& paletteColor : palette)
        {
          double distance = cv::norm(cv::Vec3d(pixel) - cv::Vec3d(paletteColor));
          if (distance < minDistance)
          {
            minDistance = distance;
            closest     = paletteColor;
          }
        }
        lookupTable[r * 256 * 256 + g * 256 + b] = closest;
      }
    }
  }

#pragma omp parallel for collapse(2)
  for (int i = 0; i < image.rows; i++)
  {
    for (int j = 0; j < image.cols; j++)
    {
      cv::Vec3b pixel           = image.at<cv::Vec3b>(i, j);
      image.at<cv::Vec3b>(i, j) = lookupTable[pixel[2] * 256 * 256 + pixel[1] * 256 + pixel[0]];
    }
  }
}

py::bytes medianCutQuantization(py::bytes          image_bytes,
                                int                numColors,
                                const std::string& output_format = "png")
{
  if (numColors <= 0) throw std::invalid_argument("Number of colors must be greater than 0");

  // Read image - use py::bytes correctly
  std::string        img_str = image_bytes;  // Converting to std::string
  std::vector<uchar> buffer(img_str.begin(), img_str.end());
  cv::Mat            image = cv::imdecode(buffer, cv::IMREAD_COLOR);

  if (image.empty()) throw std::runtime_error("Could not decode image");

  std::set<cv::Vec3b, Vec3bComparator> uniqueColors;

  const int num_threads = std::thread::hardware_concurrency();
  std::vector<std::set<cv::Vec3b, Vec3bComparator>> thread_colors(num_threads);

#pragma omp parallel num_threads(num_threads)
  {
    int thread_id = omp_get_thread_num();
#pragma omp for collapse(2) nowait
    for (int i = 0; i < image.rows; i++)
      for (int j = 0; j < image.cols; j++)
        thread_colors[thread_id].insert(image.at<cv::Vec3b>(i, j));
  }

  // Merge thread-local sets
  for (auto& local_set : thread_colors) uniqueColors.insert(local_set.begin(), local_set.end());

  if (numColors > uniqueColors.size())
    throw std::invalid_argument("Requested number of colors (" + std::to_string(numColors) +
                                ") is greater than unique colors in image (" +
                                std::to_string(uniqueColors.size()) + ")");

  if (numColors == uniqueColors.size())
  {
    std::cout << "Number of colors requested is equal to unique colors in "
                 "image. No quantization needed."
              << std::endl;
    return image_bytes;
  }

  std::vector<ColorBox> boxes;
  processInitialBox(image, boxes);
  splitBoxes(boxes, numColors);
  createLookupTableAndMapColors(image, boxes);

  // Determine file extension and compression parameters based on output format
  std::string      file_extension;
  std::vector<int> compression_params;

  if (output_format == "jpeg" || output_format == "jpg")
  {
    file_extension     = ".jpg";
    compression_params = {cv::IMWRITE_JPEG_QUALITY, 95};  // High quality JPEG
  }
  else
  {
    // Default to PNG for any other format (including "png")
    file_extension     = ".png";
    compression_params = {cv::IMWRITE_PNG_COMPRESSION, 1};
  }

  // Encode result to py::bytes
  std::vector<uchar> buffer_result;
  if (!cv::imencode(file_extension, image, buffer_result, compression_params))
    throw std::runtime_error("Could not encode result image to " + output_format +
                             " format in median cut quantization");
  return py::bytes(reinterpret_cast<const char*>(buffer_result.data()), buffer_result.size());
}

// Module definition
PYBIND11_MODULE(median_cut_cpp, m)
{
  m.def("medianCutQuantization",
        &medianCutQuantization,
        py::arg("image_bytes"),
        py::arg("num_colors"),
        py::arg("output_format") = "png",
        "Performs median cut quantization on an image");
}
