#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>

#include <iostream>
#include <opencv2/opencv.hpp>
#include <vector>

namespace py = pybind11;

/*****************************************
 ****         HELPER FUNCTIONS        ****
 *****************************************/

inline cv::Mat decode_image(const py::bytes& image_bytes)
{
  try
  {
    char*   buffer_ptr;
    ssize_t length;

    // Extract raw pointer from Python bytes
    if (PYBIND11_BYTES_AS_STRING_AND_SIZE(image_bytes.ptr(), &buffer_ptr, &length))
      throw std::runtime_error("Failed to extract bytes data");

    // Create a vector view of the data for imdecode (using pointer directly
    // sometimes fails)
    std::vector<uchar> buffer(reinterpret_cast<uchar*>(buffer_ptr),
                              reinterpret_cast<uchar*>(buffer_ptr) + length);

    // Decode the image
    cv::Mat img = cv::imdecode(buffer, cv::IMREAD_COLOR);

    if (img.empty()) throw std::runtime_error("Failed to decode image");

    return img;
  }
  catch (const cv::Exception& e)
  {
    throw std::runtime_error(std::string("OpenCV error: ") + e.what());
  }
}

inline py::bytes encode_image(const cv::Mat& image, const std::string& format)
{
  std::string ext = (format == "png") ? ".png" : ".jpg";

  // Preallocate buffer based on image size (rough estimate)
  std::vector<uchar> buffer;
  buffer.reserve(image.total() * image.elemSize());

  // Encode
  cv::imencode(ext, image, buffer);

  // Create Python bytes directly from buffer data
  auto result = py::bytes(reinterpret_cast<char*>(buffer.data()), buffer.size());

  return result;
}

/*****************************************
 ****           MAIN FUNCTIONS        ****
 *****************************************/

/**
 * Invert the colors of an image - vectorized implementation
 *
 * @param image_bytes Input image as bytes
 * @param output_format Output image format (e.g., "jpeg", "png")
 * @return Image with inverted colors as bytes
 */
py::bytes invert_colors(py::bytes image_bytes, const std::string& output_format = "jpeg")
{
  try
  {
    cv::Mat image = decode_image(image_bytes);

    // Invert colors using bitwise not
    cv::Mat result;
    cv::bitwise_not(image, result);

    return encode_image(result, output_format);
  }
  catch (const std::exception& e)
  {
    throw std::runtime_error(std::string("Error in invert_colors: ") + e.what());
  }
}

/**
 * Adjust the hue and saturation of an image - vectorized implementation
 *
 * @param image_bytes Input image as bytes
 * @param hue_shift Hue shift in range [-1.0, 1.0]
 * @param saturation_factor Saturation adjustment factor (1.0 = no change)
 * @param output_format Output image format (e.g., "jpeg", "png")
 * @return Image with adjusted hue and saturation as bytes
 */
py::bytes adjust_hue_saturation(py::bytes          image_bytes,
                                float              hue_shift,
                                float              saturation_factor,
                                const std::string& output_format = "jpeg")
{
  try
  {
    // Decode image efficiently
    cv::Mat image = decode_image(image_bytes);

    // Convert to HSV
    cv::Mat hsv_image;
    cv::cvtColor(image, hsv_image, cv::COLOR_BGR2HSV);

    // Calculate hue shift value (OpenCV uses 0-180 range for H channel)
    int hue_shift_value = static_cast<int>(hue_shift * 180);

    // Choose the most efficient processing method based on matrix structure
    if (hsv_image.isContinuous())
    {
      // Direct pixel access - fastest for continuous memory
      uchar*    data_ptr     = hsv_image.data;
      const int total_pixels = hsv_image.total();

// Use OpenMP for parallel processing
#pragma omp parallel for
      for (int i = 0; i < total_pixels; i++)
      {
        // Each pixel has 3 channels (H,S,V)
        int idx = i * 3;

        // H channel (0-179 in OpenCV)
        int h = (data_ptr[idx] + hue_shift_value) % 180;
        if (h < 0) h += 180;  // Handle negative wrap-around
        data_ptr[idx] = static_cast<uchar>(h);

        // S channel (scale with saturation factor)
        float s           = data_ptr[idx + 1] * saturation_factor;
        data_ptr[idx + 1] = static_cast<uchar>(std::min(255.0f, std::max(0.0f, s)));

        // V channel remains unchanged
      }
    }
    else
    {
      // Use vectorized operations from OpenCV for non-continuous matrices
      // Create a fully vectorized version using LUT and matrix operations

      // Split channels for separate processing
      std::vector<cv::Mat> hsv_channels(3);
      cv::split(hsv_image, hsv_channels);

      // Process H channel with LUT
      cv::Mat lut_h = cv::Mat::zeros(1, 256, CV_8U);
      for (int i = 0; i < 180; i++)
      {
        int h = (i + hue_shift_value) % 180;
        if (h < 0) h += 180;
        lut_h.at<uchar>(i) = static_cast<uchar>(h);
      }
      // Fill the rest of the LUT (for safety)
      for (int i = 180; i < 256; i++)
      {
        lut_h.at<uchar>(i) = static_cast<uchar>(i);
      }

      // Apply LUT to H channel
      cv::LUT(hsv_channels[0], lut_h, hsv_channels[0]);

      // Process S channel with matrix operations
      hsv_channels[1].convertTo(hsv_channels[1], -1, saturation_factor);

      // Merge channels back
      cv::merge(hsv_channels, hsv_image);
    }

    // Convert back to BGR
    cv::Mat result;
    cv::cvtColor(hsv_image, result, cv::COLOR_HSV2BGR);

    // Return encoded result
    return encode_image(result, output_format);
  }
  catch (const std::exception& e)
  {
    throw std::runtime_error(std::string("Error in adjust_hue_saturation: ") + e.what());
  }
}

/**
 * Convert an image to grayscale
 *
 * @param image_bytes Input image as bytes
 * @param output_format Output image format (e.g., "jpeg", "png")
 * @return Grayscale image as bytes
 */
py::bytes convert_to_grayscale(py::bytes image_bytes, const std::string& output_format = "jpeg")
{
  try
  {
    cv::Mat image = decode_image(image_bytes);
    cv::Mat gray_image;

    // Skip BGR2GRAY->GRAY2BGR conversion if not needed
    if (output_format == "jpeg" || output_format == "jpg")
    {
      // JPEG requires BGR or RGB format, so we need to convert back
      cv::cvtColor(image, gray_image, cv::COLOR_BGR2GRAY);
      cv::Mat result;
      cv::cvtColor(gray_image, result, cv::COLOR_GRAY2BGR);
      return encode_image(result, output_format);
    }
    else
    {
      // For PNG, we can keep it grayscale (single channel)
      cv::cvtColor(image, gray_image, cv::COLOR_BGR2GRAY);
      return encode_image(gray_image, output_format);
    }
  }
  catch (const std::exception& e)
  {
    throw std::runtime_error(std::string("Error in convert_to_grayscale: ") + e.what());
  }
}

/*****************************************
 ****         PYTHON BINDINGS         ****
 *****************************************/

PYBIND11_MODULE(image_editing_cpp, m)
{
  m.doc() =
      "Image editing functions using OpenCV\n"
      "    adjust_hue_saturation: Adjust hue and saturation of an image\n"
      "    invert_colors: Invert the colors of an image\n"
      "    convert_to_grayscale: Convert an image to grayscale\n";

  m.def("adjust_hue_saturation",
        &adjust_hue_saturation,
        py::arg("image_bytes"),
        py::arg("hue_shift"),
        py::arg("saturation_factor"),
        py::arg("output_format") = "jpeg",
        R"(
        Adjust the hue and saturation of an image

        Parameters:
            image_bytes (bytes): Input image data
            hue_shift (float): Hue shift in range [-1.0, 1.0]
            saturation_factor (float): Saturation adjustment factor (1.0 = no change, 0.0 = grayscale, 2.0 = double saturation)
            output_format (str, optional): Output format, 'jpeg' or 'png', default is 'jpeg'

        Returns:
            bytes: Processed image data
        )");

  m.def("invert_colors",
        &invert_colors,
        py::arg("image_bytes"),
        py::arg("output_format") = "jpeg",
        R"(
        Invert the colors of an image.

        Parameters:
            image_bytes (bytes): Input image data
            output_format (str, optional): Output format, 'jpeg' or 'png', default is 'jpeg'

        Returns:
            bytes: Processed image with inverted colors
        )");

  m.def("convert_to_grayscale",
        &convert_to_grayscale,
        py::arg("image_bytes"),
        py::arg("output_format") = "jpeg",
        R"(
          Convert an image to grayscale.

          Parameters:
              image_bytes (bytes): Input image data
              output_format (str, optional): Output format, 'jpeg' or 'png', default is 'jpeg'

          Returns:
              bytes: Grayscale image data
          )");
}
