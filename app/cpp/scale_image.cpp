#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>

#include <iostream>
#include <opencv2/opencv.hpp>
#include <vector>

namespace py = pybind11;

void initialize_opencv()
{
  // cv::setUseIPP(cv::ippICVEnabled()); // Needs to be fixed for intel vector
  // support cv::setNumThreads(0);  // Does not seem to be very fast and might
  // be a problem on a multi-user back-end
}

/*********************************************************
 ************         HELPER FUNCTIONS        ************
 *********************************************************/

/**
 * @brief Helper function to decode image bytes to OpenCV Mat
 */
inline cv::Mat decode_image(const py::bytes& image_bytes)
{
  try
  {
    // Extract raw bytes
    char*   buffer_ptr;
    ssize_t length;
    if (PYBIND11_BYTES_AS_STRING_AND_SIZE(image_bytes.ptr(), &buffer_ptr, &length))
      throw std::runtime_error("Failed to extract bytes data");

    if (length == 0) throw std::runtime_error("Empty image bytes received");

    // Create Mat from raw buffer
    cv::Mat raw_data(1, length, CV_8UC1, buffer_ptr);
    cv::Mat img = cv::imdecode(raw_data, cv::IMREAD_UNCHANGED);

    if (img.empty()) throw std::runtime_error("Failed to decode image: empty result");

    return img;
  }
  catch (const std::exception& e)
  {
    throw std::runtime_error(std::string("Error decoding image: ") + e.what());
  }
}

/**
 * @brief Helper function to encode OpenCV Mat to image bytes
 */
inline py::bytes encode_image(const cv::Mat& image, const std::string& format)
{
  std::string ext;
  if (format == "png")
  {
    ext = ".png";
  }
  else if (format == "jpg" || format == "jpeg")
  {
    ext = ".jpg";
  }
  else if (format == "bmp")
  {
    ext = ".bmp";
  }
  else if (format == "tiff")
  {
    ext = ".tiff";
  }
  else
  {
    throw std::runtime_error("Unsupported output format: " + format);
  }

  std::vector<uchar> buffer;
  // Ensure enough space is reserved
  buffer.reserve(image.total() * image.elemSize());
  bool imencode_success = cv::imencode(ext, image, buffer);
  if (!imencode_success)
  {
    throw std::runtime_error("Error in encode_image (cv::imencode failed)");
  }
  return py::bytes(std::string(buffer.begin(), buffer.end()));
}

/*********************************************************
 ************         MAIN FUNCTIONS          ************
 *********************************************************/

/**
 * Resize an image to scale down (reduce pixels) while maintaining visual
 * appearance.
 */
py::bytes resize_image(py::bytes          image_bytes,
                       float              scale_factor,
                       const std::string& output_format = "jpeg")
{
  try
  {
    // Decode image
    cv::Mat image = decode_image(image_bytes);

    // Calculate new dimensions
    int height     = image.rows;
    int width      = image.cols;
    int new_height = cvRound(height * scale_factor);
    int new_width  = cvRound(width * scale_factor);

    // Create output image with new dimensions
    cv::Mat resized;

    // Use proper interpolation method based on scaling direction
    int interpolation = (scale_factor < 1.0) ? cv::INTER_AREA : cv::INTER_CUBIC;

    // Perform resize operation
    cv::resize(image, resized, cv::Size(new_width, new_height), 0, 0, interpolation);

    // Return encoded result
    return encode_image(resized, output_format);
  }
  catch (const std::exception& e)
  {
    throw std::runtime_error(std::string("Error in resize_image: ") + e.what());
  }
}

/**
 * Scale an image by properly resizing it according to scale factor
 */
py::bytes scale_image(py::bytes          image_bytes,
                      float              scale_factor,
                      const std::string& output_format = "jpeg")
{
  // This function is generally thread-safe, assuming the underlying OpenCV
  // functions are used correctly and the input/output byte arrays are
  // properly managed in a multi-threaded environment.
  try
  {
    if (scale_factor == 1.0)
    {
      // No scaling needed, return original bytes
      return image_bytes;
    }
    else if (scale_factor <= 0.001f)
    {
      throw std::invalid_argument("Invalid scale factor: must be positive");
    }
    else if (scale_factor > 15)
    {
      throw std::invalid_argument("Invalid scale factor: must be less than 15");
    }
    else
    {
      // Use resize_image for all scale factors (both up and down)
      return resize_image(image_bytes, scale_factor, output_format);
    }
  }
  catch (const std::exception& e)
  {
    throw std::runtime_error(std::string("Error in scale_image: ") + e.what());
  }
}

/*********************************************************
 ************         PYTHON BINDINGS         ************
 *********************************************************/

PYBIND11_MODULE(scale_image_cpp, m)
{
  m.doc() = "Image scaling functions using OpenCV";

  m.def("resize_image",
        &resize_image,
        py::arg("image_bytes"),
        py::arg("scale_factor"),
        py::arg("output_format") = "jpeg",
        "Resize an image to scale up or down while maintaining visual "
        "appearance");

  m.def("scale_image",
        &scale_image,
        py::arg("image_bytes"),
        py::arg("scale_factor"),
        py::arg("output_format") = "jpeg",
        "Scale an image by properly resizing it according to scale factor");
}
