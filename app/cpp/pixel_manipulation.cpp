#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include <iostream>
#include <opencv2/opencv.hpp>
#include <unordered_map>
#include <unordered_set>
#include <vector>

namespace py = pybind11;

/*********************************************************
 ************         HELPER FUNCTIONS        ************
 *********************************************************/

inline cv::Mat decode_image(const py::bytes& image_bytes)
{
  try
  {
    // Extract raw bytes directly (zero-copy)
    char*   buffer_ptr;
    ssize_t length;
    if (PYBIND11_BYTES_AS_STRING_AND_SIZE(image_bytes.ptr(), &buffer_ptr, &length))
      throw std::runtime_error("Failed to extract bytes data from py::bytes");

    if (length == 0) throw std::runtime_error("Empty image bytes received");

    // Create Mat from raw buffer (no copy yet)
    cv::Mat raw_data(1, length, CV_8UC1, buffer_ptr);
    cv::Mat img = cv::imdecode(raw_data, cv::IMREAD_COLOR);

    if (img.empty()) throw std::runtime_error("Failed to decode image: empty result");

    return img;
  }
  catch (const cv::Exception& e)
  {
    throw std::runtime_error(std::string("OpenCV error: ") + e.what());
  }
  catch (const std::exception& e)
  {
    throw std::runtime_error(std::string("Standard error: ") + e.what());
  }
  catch (...)
  {
    throw std::runtime_error("Unknown error in decode_image");
  }
}

// Helper function to encode image to bytes
inline py::bytes encode_image(const cv::Mat& image, const std::string& format)
{
  std::string ext = (format == "png") ? ".png" : ".jpg";

  // Preallocate buffer based on image size
  std::vector<uchar> buffer;
  buffer.reserve(image.total() * image.elemSize());

  cv::imencode(ext, image, buffer);

  return py::bytes(std::string(buffer.begin(), buffer.end()));
}

inline std::string bgr_to_hex(const cv::Vec3b& bgr)
{
  char hex[8];
  sprintf(hex, "#%02x%02x%02x", bgr[2], bgr[1],
          bgr[0]);  // Convert BGR to RGB for hex
  return std::string(hex);
}

inline cv::Vec3b hex_to_bgr(const std::string& hex)
{
  unsigned int r, g, b;
  if (hex[0] == '#')
    sscanf(hex.c_str() + 1, "%02x%02x%02x", &r, &g, &b);
  else
    sscanf(hex.c_str(), "%02x%02x%02x", &r, &g, &b);
  return cv::Vec3b(b, g, r);  // Return as BGR for OpenCV
}

// Hash function for Vec3b to use in unordered_map
struct VecHash
{
  size_t operator()(const cv::Vec3b& v) const
  {
    // Combine the three values into a single hash
    return ((size_t)v[0] << 16) | ((size_t)v[1] << 8) | (size_t)v[2];
  }
};

// Equality comparison for Vec3b to use in unordered_map
struct VecEqual
{
  bool operator()(const cv::Vec3b& a, const cv::Vec3b& b) const
  {
    return a[0] == b[0] && a[1] == b[1] && a[2] == b[2];
  }
};

struct Vec3bComparator
{
  bool operator()(const cv::Vec3b& a, const cv::Vec3b& b) const
  {
    if (a[0] != b[0]) return a[0] < b[0];
    if (a[1] != b[1]) return a[1] < b[1];
    return a[2] < b[2];
  }
};

/*********************************************************
 ************         MAIN FUNCTIONS          ************
 *********************************************************/

/**
 * Extract all unique colors from an image
 *
 * @param image_bytes Input image as bytes
 * @return List of unique colors as hex strings or RGB tuples
 */
py::object extract_colors(py::bytes image_bytes)
{
  try
  {
    cv::Mat image = decode_image(image_bytes);

    if (image.type() != CV_8UC3)
      throw std::runtime_error("Image must be 8-bit 3-channel (type CV_8UC3)");

    std::set<cv::Vec3b, Vec3bComparator> uniqueColors;
    for (int i = 0; i < image.rows; i++)
      for (int j = 0; j < image.cols; j++) uniqueColors.insert(image.at<cv::Vec3b>(i, j));

    py::list result;
    for (const auto& color : uniqueColors) result.append(bgr_to_hex(color));

    return result;
  }
  catch (const std::exception& e)
  {
    throw std::runtime_error(std::string("Error in extract_colors: ") + e.what());
  }
}

/**
 * Replace specific colors in an image with texturizing option
 *
 * @param image_bytes Input image as bytes
 * @param color_map Dictionary mapping original colors to replacement colors
 * @param texturize Value between 1-256; 1 = exact color only, 256 = all colors
 * @param output_format Output image format (e.g., "jpeg", "png")
 * @return Image with replaced colors as bytes
 */
py::bytes replace_colors(py::bytes                                 image_bytes,
                         const std::map<std::string, std::string>& color_map,
                         int                                       texturize     = 256,
                         const std::string&                        output_format = "jpeg")
{
  try
  {
    // Validate texturize value
    if (texturize < 1 || texturize > 256)
      throw std::invalid_argument("Texturize value must be between 1 and 256");

    // Decode image
    cv::Mat image = decode_image(image_bytes);

    // Create a copy of the image to modify
    cv::Mat result = image.clone();

    // Convert color_map from hex strings to BGR values
    std::vector<std::pair<cv::Vec3b, cv::Vec3b>> color_pairs;
    for (const auto& kv : color_map)
    {
      cv::Vec3b src_color = hex_to_bgr(kv.first);
      cv::Vec3b dst_color = hex_to_bgr(kv.second);
      color_pairs.push_back(std::make_pair(src_color, dst_color));
    }

    // Calculate color similarity threshold based on texturize value
    int threshold = (texturize - 1) * 3;  // Threshold increases with texturize, 0 to 765

    // Process the image
    if (result.isContinuous())
    {
      // Fast path for continuous data
      cv::Vec3b* ptr          = result.ptr<cv::Vec3b>(0);
      const int  total_pixels = result.total();

#pragma omp parallel for
      for (int i = 0; i < total_pixels; i++)
      {
        cv::Vec3b& pixel = ptr[i];

        // Check each color in the map for similarity
        for (const auto& color_pair : color_pairs)
        {
          const cv::Vec3b& src_color = color_pair.first;
          const cv::Vec3b& dst_color = color_pair.second;

          // Calculate color distance (Manhattan distance for speed)
          int distance = std::abs(pixel[0] - src_color[0]) + std::abs(pixel[1] - src_color[1]) +
                         std::abs(pixel[2] - src_color[2]);

          // If the color is similar enough based on texturize threshold,
          // replace it
          if (distance <= threshold)
          {
            // If texturize is partial, blend the color proportionally to how
            // close it is
            if (texturize > 1 && texturize < 256)
            {
              // Calculate blend factor (closer colors get more replacement)
              float blend = 1.0f - (static_cast<float>(distance) / threshold);

              // Blend the color
              pixel[0] = static_cast<uchar>(blend * dst_color[0] + (1.0f - blend) * pixel[0]);
              pixel[1] = static_cast<uchar>(blend * dst_color[1] + (1.0f - blend) * pixel[1]);
              pixel[2] = static_cast<uchar>(blend * dst_color[2] + (1.0f - blend) * pixel[2]);
            }
            else
            {
              // If texturize is at minimum or maximum, just replace the color
              pixel = dst_color;
            }

            // Break after first match
            break;
          }
        }
      }
    }
    else
    {
      // Generic path for non-continuous data
      for (int y = 0; y < result.rows; y++)
      {
        for (int x = 0; x < result.cols; x++)
        {
          cv::Vec3b& pixel = result.at<cv::Vec3b>(y, x);

          // Check each color in the map for similarity
          for (const auto& color_pair : color_pairs)
          {
            const cv::Vec3b& src_color = color_pair.first;
            const cv::Vec3b& dst_color = color_pair.second;

            // Calculate color distance
            int distance = std::abs(pixel[0] - src_color[0]) + std::abs(pixel[1] - src_color[1]) +
                           std::abs(pixel[2] - src_color[2]);

            // If the color is similar enough based on texturize threshold,
            // replace it
            if (distance <= threshold)
            {
              // If texturize is partial, blend the color proportionally
              if (texturize > 1 && texturize < 256)
              {
                // Calculate blend factor
                float blend = 1.0f - (static_cast<float>(distance) / threshold);

                // Blend the color
                pixel[0] = static_cast<uchar>(blend * dst_color[0] + (1.0f - blend) * pixel[0]);
                pixel[1] = static_cast<uchar>(blend * dst_color[1] + (1.0f - blend) * pixel[1]);
                pixel[2] = static_cast<uchar>(blend * dst_color[2] + (1.0f - blend) * pixel[2]);
              }
              else
              {
                // If texturize is at minimum or maximum, just replace the color
                pixel = dst_color;
              }

              // Break after first match
              break;
            }
          }
        }
      }
    }

    // Return encoded result
    return encode_image(result, output_format);
  }
  catch (const std::exception& e)
  {
    throw std::runtime_error(std::string("Error in replace_colors: ") + e.what());
  }
}

/*********************************************************
 ************         PYTHON BINDINGS         ************
 *********************************************************/

PYBIND11_MODULE(pixel_manipulation_cpp, m)
{
  m.doc() = "Pixel and color manipulation functions for images";

  m.def("extract_colors",
        &extract_colors,
        py::arg("image_bytes"),
        R"(
          Extract all unique colors from an image.

          Parameters:
              image_bytes (bytes): Input image data

          Returns:
              list: List of unique colors as hex strings or RGB tuples
          )");

  m.def("replace_colors",
        &replace_colors,
        py::arg("image_bytes"),
        py::arg("color_map"),
        py::arg("texturize")     = 256,
        py::arg("output_format") = "jpeg",
        R"(
          Replace specific colors in an image with texturizing option.

          Parameters:
              image_bytes (bytes): Input image data
              color_map (dict): Dictionary mapping original colors to replacement colors
                                (keys and values are hex color strings)
              texturize (int): Value between 1-256 that controls color precision
                              (1=most reduction, 256=no reduction)
              output_format (str): Output format ('jpeg' or 'png')

          Returns:
              bytes: Processed image with replaced colors
          )");
}
