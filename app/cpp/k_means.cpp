/**
 * @file k_means.cpp
 * @brief K-means color quantization implementation.
 *
 * @details
 * The implementation uses standard C++ libraries and the libpng library for
 * image handling. It is designed to be efficient and can be used as a module in
 * larger image processing applications.
 *
 * <AUTHOR> Agrawal
 * @date 2025-03
 * @version 1.1
 */

#include <immintrin.h>
#include <png.h>
#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>

#include <algorithm>
#include <chrono>
#include <cmath>
#include <functional>
#include <iostream>
#include <limits>
#include <opencv2/opencv.hpp>
#include <unordered_set>
#include <vector>

using namespace std;
namespace py = pybind11;

struct Color
{
  std::array<unsigned char, 3> components;  // [r,g,b]
  unsigned char&               r = components[0];
  unsigned char&               g = components[1];
  unsigned char&               b = components[2];

  // Default constructor
  Color() = default;

  // Copy constructor - use member initializer list for efficiency
  Color(const Color& other) : components(other.components) {}

  // Assignment operator - use array assignment for consistency
  Color& operator=(const Color& other)
  {
    if (this != &other)
    {  // Self-assignment check
      components = other.components;
    }
    return *this;
  }
};

double colorDistance(const Color& c1, const Color& c2)
{
  return std::sqrt(std::pow(c1.r - c2.r, 2) + std::pow(c1.g - c2.g, 2) + std::pow(c1.b - c2.b, 2));
}

// K-Means Clustering (Simplified Implementation)
std::vector<Color> kmeans(std::vector<Color>* points, int numClusters)
{
  if (!points || points->empty() || numClusters <= 0) return {};

  numClusters = std::min(numClusters, static_cast<int>(points->size()));

  std::vector<Color> centroids(numClusters);
  std::vector<int>   assignments(points->size());

  // K-means++ initialization
  Color initialCentroid = (*points)[rand() % points->size()];
  centroids[0]          = initialCentroid;
  for (int k = 1; k < numClusters; k++)
  {
    std::vector<double> distances(points->size());
    double              total = 0;

    // Calculate distances to nearest existing centroid
    for (size_t i = 0; i < points->size(); i++)
    {
      double minDist = std::numeric_limits<double>::max();
      for (int j = 0; j < k; j++)
      {
        double dist = colorDistance((*points)[i], centroids[j]);
        minDist     = std::min(minDist, dist);
      }
      distances[i] = minDist * minDist;
      total += distances[i];
    }

    double r            = (double)rand() / RAND_MAX * total;
    int    nextCentroid = 0;
    for (size_t i = 0; i < points->size(); i++)
    {
      r -= distances[i];
      if (r <= 0)
      {
        nextCentroid = i;
        break;
      }
    }
    centroids[k] = (*points)[nextCentroid];
  }

  bool changed;
  int  maxIterations = 100;

  for (int iteration = 0; iteration < maxIterations; ++iteration)
  {
    changed = false;

// Assign points to nearest centroid
#pragma omp parallel for
    for (size_t i = 0; i < points->size(); ++i)
    {
      int    oldAssignment = assignments[i];
      double minDistance   = std::numeric_limits<double>::max();
      int    bestCluster   = 0;

      for (int j = 0; j < numClusters; ++j)
      {
        double distance = colorDistance((*points)[i], centroids[j]);
        if (distance < minDistance)
        {
          minDistance = distance;
          bestCluster = j;
        }
      }

      if (oldAssignment != bestCluster)
      {
        assignments[i] = bestCluster;
        changed        = true;
      }
    }

    if (!changed) break;

    // Update centroids
    std::vector<long> sumR(numClusters, 0);
    std::vector<long> sumG(numClusters, 0);
    std::vector<long> sumB(numClusters, 0);
    std::vector<int>  counts(numClusters, 0);

    for (size_t i = 0; i < points->size(); ++i)
    {
      int cluster = assignments[i];
      sumR[cluster] += (*points)[i].r;
      sumG[cluster] += (*points)[i].g;
      sumB[cluster] += (*points)[i].b;
      counts[cluster]++;
    }

    // Calculate new centroids
    for (int j = 0; j < numClusters; ++j)
    {
      if (counts[j] > 0)
      {
        centroids[j].r = static_cast<unsigned char>(sumR[j] / counts[j]);
        centroids[j].g = static_cast<unsigned char>(sumG[j] / counts[j]);
        centroids[j].b = static_cast<unsigned char>(sumB[j] / counts[j]);
      }
    }
  }

  return centroids;
}

// Function to assign each pixel to the closest color in the palette
// Function to assign each pixel to the closest color in the palette
void ColorAssignment(
    std::vector<Color>& palette, int height, png_bytep* rowPointers, int width, int bytesPerPixel)
{
  // Precompute palette size for efficiency
  int paletteSize = static_cast<int>(palette.size());
  for (int y = 0; y < height; ++y)
  {
    png_bytep row = rowPointers[y];
    for (int x = 0; x < width; ++x)
    {
      png_bytep ptr = &(row[x * bytesPerPixel]);
      Color     color;
      if (bytesPerPixel >= 3)
      {
        color.r = ptr[0];
        color.g = ptr[1];
        color.b = ptr[2];
      }
      else if (bytesPerPixel == 1)
      {
        color.r = color.g = color.b = ptr[0];
      }
      else
      {
        throw std::runtime_error("Unsupported bytes per pixel.");
      }

      // Find the closest color by comparing squared distances (avoid sqrt)
      int          bestIndex = 0;
      unsigned int minDist   = std::numeric_limits<unsigned int>::max();
      for (int i = 0; i < paletteSize; ++i)
      {
        int          dr   = color.r - palette[i].r;
        int          dg   = color.g - palette[i].g;
        int          db   = color.b - palette[i].b;
        unsigned int dist = dr * dr + dg * dg + db * db;
        if (dist < minDist)
        {
          minDist   = dist;
          bestIndex = i;
          if (dist == 0) break;  // Perfect match, early exit
        }
      }

      ptr[0] = palette[bestIndex].r;
      ptr[1] = palette[bestIndex].g;
      ptr[2] = palette[bestIndex].b;
    }
  }
}

// Function to extract unique colors from the image
std::unique_ptr<std::vector<Color>> getUniqueColors(png_bytep* rowPointers,
                                                    int        width,
                                                    int        height,
                                                    int        bytesPerPixel)
{
  if (!rowPointers || width <= 0 || height <= 0 || (bytesPerPixel != 1 && bytesPerPixel != 3))
    return nullptr;

  auto                         uniqueColors = std::make_unique<std::vector<Color>>();
  std::unordered_set<uint32_t> seenColors;
  uniqueColors->reserve(width * height);

  for (int y = 0; y < height; ++y)
  {
    png_bytep row = rowPointers[y];
    for (int x = 0; x < width; ++x)
    {
      png_bytep ptr = &(row[x * bytesPerPixel]);
      Color     color;

      if (bytesPerPixel >= 3)
      {
        color.r = ptr[0];
        color.g = ptr[1];
        color.b = ptr[2];
      }
      else
      {
        color.r = color.g = color.b = ptr[0];
      }

      uint32_t colorKey = (color.r << 16) | (color.g << 8) | color.b;
      if (seenColors.find(colorKey) == seenColors.end())
      {
        seenColors.insert(colorKey);
        uniqueColors->push_back(color);
      }
    }
  }

  return uniqueColors;
}

// Limit colors in an image using k-means clustering directly from NumPy bytes
py::bytes limit_image_colors(
    py::array_t<unsigned char> inputData, int width, int height, int channels, int numColors)
{
  // Access raw buffer
  auto buf = inputData.request();
  if (buf.size != static_cast<py::ssize_t>(width * height * channels))
    throw std::runtime_error("Input data size mismatch.");
  unsigned char* dataPtr = static_cast<unsigned char*>(buf.ptr);

  // Build row pointers
  std::vector<png_bytep> rowPointers(height);
  for (int y = 0; y < height; ++y) rowPointers[y] = dataPtr + y * width * channels;

  // Extract unique colors
  std::unique_ptr<std::vector<Color>> uniqueColorsPtr =
      getUniqueColors(rowPointers.data(), width, height, channels);
  if (!uniqueColorsPtr) throw std::runtime_error("Failed to extract unique colors.");

  // Run k-means to get palette
  std::vector<Color> palette = kmeans(uniqueColorsPtr.get(), numColors);
  if (palette.empty()) throw std::runtime_error("K-means failed or no unique colors found.");

  // Assign pixels to closest palette colors
  try
  {
    ColorAssignment(palette, height, rowPointers.data(), width, channels);
  }
  catch (const std::exception& e)
  {
    throw std::runtime_error("Color assignment failed: " + std::string(e.what()));
  }

  // Return modified image as Python bytes
  return py::bytes(reinterpret_cast<char*>(dataPtr), width * height * channels);
}

// Module definition
PYBIND11_MODULE(k_means_cpp, m)
{
  m.def("limit_image_colors",
        &limit_image_colors,
        py::arg("inputData"),
        py::arg("width"),
        py::arg("height"),
        py::arg("channels"),
        py::arg("numColors"),
        "Limit colors in an image using k-means clustering directly from NumPy "
        "bytes");
}
