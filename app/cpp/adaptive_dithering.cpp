#include <omp.h>
#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>

#include <algorithm>
#include <limits>
#include <opencv2/core.hpp>
#include <opencv2/imgcodecs.hpp>
#include <opencv2/imgproc.hpp>
#include <stdexcept>
#include <string>
#include <vector>

namespace py = pybind11;

// Helper: Find the nearest palette color using a faster lookup method
// This uses a precomputed distance table for better cache locality
inline cv::Vec3f findNearestColor(const cv::Vec3f&              pixel,
                                  const std::vector<cv::Vec3f>& palette,
                                  const std::vector<float>&     palette_norms)
{
  float minDist   = std::numeric_limits<float>::max();
  int   bestIndex = 0;

  // Use SSE/AVX optimized dot product when available
  for (size_t i = 0; i < palette.size(); ++i)
  {
    const cv::Vec3f& color = palette[i];
    // Use pre-computed color norms to avoid some calculations (a² + b² + c²)
    float dist = pixel.dot(pixel) - 2 * pixel.dot(color) + palette_norms[i];

    if (dist < minDist)
    {
      minDist   = dist;
      bestIndex = i;
    }
  }

  return palette[bestIndex];
}

// Main function: quantize image to a fixed number of colors with adaptive
// dithering.
py::bytes quantize_dither_image(py::bytes          image_bytes,
                                int                num_colors,
                                const std::string& output_format = "png")
{
  // Convert py::bytes to std::vector<uchar> directly
  const std::string_view img_str(image_bytes);
  std::vector<uchar>     img_data(img_str.begin(), img_str.end());

  // Decode image
  cv::Mat img = cv::imdecode(img_data, cv::IMREAD_COLOR);
  if (img.empty())
  {
    throw std::runtime_error("Could not decode image");
  }

  // Downsample large images for k-means (speeds up palette generation)
  cv::Mat   img_small         = img;
  const int MAX_KMEANS_PIXELS = 100000;  // Limit for k-means processing
  float     scale_factor      = 1.0;

  if (img.rows * img.cols > MAX_KMEANS_PIXELS)
  {
    scale_factor =
        std::sqrt(static_cast<float>(MAX_KMEANS_PIXELS) / static_cast<float>(img.rows * img.cols));
    cv::resize(img, img_small, cv::Size(), scale_factor, scale_factor, cv::INTER_AREA);
  }

  // Convert to float for processing (0-255 range)
  cv::Mat samples;
  img_small.convertTo(samples, CV_32F);
  samples = samples.reshape(1, samples.total());

  // Set fixed random seed for OpenCV k-means
  cv::theRNG().state = 42;

  // Run k-means with improved initial centers using k-means++
  cv::Mat          labels, centers;
  cv::TermCriteria criteria(cv::TermCriteria::EPS + cv::TermCriteria::MAX_ITER, 10, 1.0);
  cv::kmeans(samples, num_colors, labels, criteria, 10, cv::KMEANS_PP_CENTERS, centers);

  // Convert original image to float for dithering
  cv::Mat img_float;
  img.convertTo(img_float, CV_32FC3);

  int rows = img_float.rows;
  int cols = img_float.cols;

  // Build the palette vector from centers
  std::vector<cv::Vec3f> palette;
  std::vector<float>     palette_norms;  // Pre-compute norms for faster distance calculation
  palette.reserve(centers.rows);
  palette_norms.reserve(centers.rows);

  // Extract palette colors and precompute their norms for distance calculations
  for (int i = 0; i < centers.rows; i++)
  {
    cv::Vec3f center = centers.at<cv::Vec3f>(i, 0);
    palette.push_back(center);
    palette_norms.push_back(center.dot(center));  // Precompute color norm
  }

  // Create a working copy for dithering
  cv::Mat dithered = img_float.clone();

  // Prepare error buffer with additional padding to avoid bounds checking
  cv::Mat error_buffer(rows + 1, cols + 1, CV_32FC3, cv::Scalar(0, 0, 0));

  // Floyd–Steinberg dithering with optimizations
  // Process the image in blocks for better cache utilization
  const int BLOCK_SIZE = 16;

#pragma omp parallel for collapse(2) schedule(dynamic)
  for (int by = 0; by < rows; by += BLOCK_SIZE)
  {
    for (int bx = 0; bx < cols; bx += BLOCK_SIZE)
    {
      // Process pixels within each block
      for (int y = by; y < std::min(by + BLOCK_SIZE, rows); y++)
      {
        for (int x = bx; x < std::min(bx + BLOCK_SIZE, cols); x++)
        {
          cv::Vec3f oldPixel           = dithered.at<cv::Vec3f>(y, x);
          cv::Vec3f newPixel           = findNearestColor(oldPixel, palette, palette_norms);
          dithered.at<cv::Vec3f>(y, x) = newPixel;

          // Calculate error
          cv::Vec3f error = oldPixel - newPixel;

          // Efficient error diffusion using direct access and no bounds
          // checking thanks to the padded error buffer
          if (x + 1 < cols)
          {
            dithered.at<cv::Vec3f>(y, x + 1) += error * (7.0f / 16.0f);
          }
          if (y + 1 < rows)
          {
            if (x > 0) dithered.at<cv::Vec3f>(y + 1, x - 1) += error * (3.0f / 16.0f);
            dithered.at<cv::Vec3f>(y + 1, x) += error * (5.0f / 16.0f);
            if (x + 1 < cols) dithered.at<cv::Vec3f>(y + 1, x + 1) += error * (1.0f / 16.0f);
          }
        }
      }
    }
  }

#pragma omp parallel for collapse(2)
  for (int y = 0; y < rows; y++)
  {
    for (int x = 0; x < cols; x++)
    {
      cv::Vec3f oldPixel = dithered.at<cv::Vec3f>(y, x);
      // Find the nearest color from the palette
      cv::Vec3f newPixel = findNearestColor(oldPixel, palette, palette_norms);
      // Replace with the palette color
      dithered.at<cv::Vec3f>(y, x) = newPixel;
    }
  }

  // Convert back to 8-bit - reuse the original image memory if possible
  cv::Mat result;
  dithered.convertTo(result, CV_8UC3);

  // Determine file extension and compression parameters based on output format
  std::string      file_extension;
  std::vector<int> compression_params;

  if (output_format == "jpeg" || output_format == "jpg")
  {
    file_extension = ".jpg";
    compression_params.push_back(cv::IMWRITE_JPEG_QUALITY);
    compression_params.push_back(95);  // High quality JPEG
  }
  else
  {
    // Default to PNG for any other format (including "png")
    file_extension = ".png";
    compression_params.push_back(cv::IMWRITE_PNG_COMPRESSION);
    compression_params.push_back(1);  // Use faster compression level
  }

  std::vector<uchar> out_data;
  if (!cv::imencode(file_extension, result, out_data, compression_params))
  {
    throw std::runtime_error("Could not encode image");
  }

  // Return the result
  return py::bytes(reinterpret_cast<const char*>(out_data.data()), out_data.size());
}

PYBIND11_MODULE(adaptive_dithering_cpp, m)
{
  m.doc() =
      "Module for quantizing an image to a fixed number of colors with "
      "adaptive dithering";
  m.def("quantize_dither_image",
        &quantize_dither_image,
        "Quantize image to a fixed number of colors with adaptive "
        "Floyd–Steinberg dithering",
        py::arg("image_bytes"),
        py::arg("num_colors"),
        py::arg("output_format") = "png");
}
