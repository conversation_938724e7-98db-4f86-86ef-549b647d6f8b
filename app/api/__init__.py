from fastapi import APIRouter
from app.api.endpoints import (
    search_router,
    color_router,
    transform_router,
    health_router,
    file_uploads_router,
    final_image_router,
)

router = APIRouter()

# Include all routers from our modular endpoints
router.include_router(search_router)
router.include_router(color_router)
router.include_router(transform_router)
router.include_router(health_router)
router.include_router(file_uploads_router, tags=["File Uploads"])
router.include_router(final_image_router, tags=["Final Image"])

