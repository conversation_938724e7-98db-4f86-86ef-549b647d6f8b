import tempfile
import json
import re
from pydantic import BaseModel, RootModel, Field, field_validator
from enum import Enum
from typing import Dict, Any
from fastapi.responses import FileResponse
from PIL import Image


class SimilarImage(BaseModel):
    image_name: str
    score: float


class Algorithm(str, Enum):
    median_cut = "median_cut"
    k_means = "k_means"
    adaptive_dithering = "adaptive_dithering"


class ImageProcessingParams(BaseModel):
    hue: float = Field(0.0, description="Hue adjustment (-1.0 to 1.0)", ge=-1.0, le=1.0)
    saturation: float = Field(0.0, description="Saturation adjustment (0 to 2.0)", ge=0.0, le=2.0)


class ColorReplacements(RootModel):
    """Model for color replacement mappings.
    Directly maps source hex colors to target hex colors."""

    root: Dict[str, str]

    model_config = {
        "json_schema_extra": {"examples": [{"#FF0000": "#00FF00", "#0000FF": "#FFFF00"}]}
    }

    @field_validator("root", mode="before")
    @classmethod
    def parse_json_string(cls, data: Any) -> Dict[str, str]:
        """Parse input data from JSON string if needed."""
        if isinstance(data, str):
            try:
                parsed_data = json.loads(data)
                if isinstance(parsed_data, dict):
                    return parsed_data
                raise ValueError("Expected a dictionary mapping colors")
            except json.JSONDecodeError as e:
                raise ValueError(f"Invalid JSON string: {str(e)}")
        return data

    @field_validator("root")
    @classmethod
    def validate_hex_colors(cls, value: Dict[str, str]) -> Dict[str, str]:
        """Validate that all keys and values are valid hex colors."""
        hex_pattern = r"^#[0-9A-Fa-f]{6}$"
        for key, val in value.items():
            if not re.match(hex_pattern, key) or not re.match(hex_pattern, val):
                raise ValueError(f"Invalid hex color: {key} or {val}")
        return value


def send_file_response(data: bytes, format: str) -> FileResponse:
    """Creates and returns a FileResponse with the provided data."""
    # Use a temporary file that is automatically cleaned up
    with tempfile.NamedTemporaryFile(delete=False, suffix=f".{format}") as temp_file:
        temp_file.write(data)
        temp_file.flush()
        file_path = temp_file.name

    # Determine media type based on format
    if format == "zip":
        media_type = "application/zip"
    else:
        media_type = f"image/{format}"

    # Return the FileResponse object
    return FileResponse(
        file_path,
        media_type=media_type,
    )
