import datetime
from fastapi import APIRouter
from fastapi.responses import JSONResponse

router = APIRouter(tags=["System"])


@router.get("/health")
async def health_check():
    """Returns health check information about the API service.

    This endpoint provides a simple health check mechanism to verify that
    the API service is up and running correctly. It returns a JSON response
    with a greeting message, service status, service name, and the current timestamp.

        JSONResponse: A response with status code 200 and content containing:
            - message (str): A greeting message ("Hello world!")
            - status (str): Service status ("ok")
            - service (str): Name of the service ("mixmix-api-backend")
            - timestamp (str): Current server time in ISO format
    """
    return JSONResponse(
        status_code=200,
        content={
            "message": "Hello world!",
            "status": "ok",
            "service": "mixmix-api-backend",
            "timestamp": datetime.datetime.now().isoformat(),
        },
    )
