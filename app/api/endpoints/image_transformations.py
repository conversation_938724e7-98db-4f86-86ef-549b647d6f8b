import logging
from fastapi import APIRouter, Depends, Form, File, UploadFile, HTTPException
from fastapi.responses import FileResponse, JSONResponse

from app.api.utils import ImageProcessingParams, send_file_response
from app.api.dependencies import verify_api_key
from app.services import (
    adjust_hue_saturation as HueSat,
    convert_to_grayscale as Grayscale,
    invert_colors as Invert,
    scale_image as Scale,
)
from app.utils.image_processing_helper import image_processing_helper

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter(tags=["Image Transformations"])


@router.post("/adjust-hue-saturation", dependencies=[Depends(verify_api_key)])
async def adjust_hue_saturation(
    file: UploadFile = File(None),
    filename: str = Form(None),
    params: ImageProcessingParams = Depends(),
) -> FileResponse:
    """Adjusts the hue and saturation of an image. Provide either a file upload or a filename (not both)."""
    try:
        (
            image_data,
            image_name,
            output_format,
        ) = await image_processing_helper.prepare_image_processing(file, filename)

        logger.info(
            f"Adjusting hue/saturation for image '{image_name}' - hue: {params.hue}, saturation: {params.saturation}"
        )

        image_processor = HueSat.AdjustHueSaturation(
            image_data, image_name, params.hue, params.saturation
        )
        result = image_processor.process()

        return image_processing_helper.process_result_with_format(
            result, output_format, "adjust hue/saturation"
        )

    except HTTPException:
        raise  # Re-raise HTTP exceptions as-is
    except Exception as e:
        logger.error(f"Error adjusting hue/saturation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/convert-to-grayscale", dependencies=[Depends(verify_api_key)])
async def convert_to_grayscale(
    file: UploadFile = File(None),
    filename: str = Form(None),
) -> FileResponse:
    """Converts an image to grayscale. Provide either a file upload or a filename (not both)."""
    try:
        (
            image_data,
            image_name,
            output_format,
        ) = await image_processing_helper.prepare_image_processing(file, filename)

        logger.info(f"Converting image '{image_name}' to grayscale")

        image_processor = Grayscale.ConvertToGrayscale(image_data, image_name)
        result = image_processor.process()

        return image_processing_helper.process_result_with_format(
            result, output_format, "convert to grayscale"
        )

    except HTTPException:
        raise  # Re-raise HTTP exceptions as-is
    except Exception as e:
        logger.error(f"Error converting to grayscale: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/invert-colors", dependencies=[Depends(verify_api_key)])
async def invert_colors(
    file: UploadFile = File(None),
    filename: str = Form(None),
) -> FileResponse:
    """Inverts the colors of an image. Provide either a file upload or a filename (not both)."""
    try:
        (
            image_data,
            image_name,
            output_format,
        ) = await image_processing_helper.prepare_image_processing(file, filename)

        logger.info(f"Inverting colors for image '{image_name}'")

        image_processor = Invert.InvertColors(image_data, image_name)
        result = image_processor.process()

        return image_processing_helper.process_result_with_format(
            result, output_format, "invert colors"
        )

    except HTTPException:
        raise  # Re-raise HTTP exceptions as-is
    except Exception as e:
        logger.error(f"Error inverting colors: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/scale-image", dependencies=[Depends(verify_api_key)])
async def scale_image(
    file: UploadFile = File(None),
    filename: str = Form(None),
    scale_factor: float = Form(1, ge=0.1, le=15),
) -> FileResponse:
    """Scale an image based on the scale factor. Provide either a file upload or a filename (not both)."""
    try:
        (
            image_data,
            image_name,
            output_format,
        ) = await image_processing_helper.prepare_image_processing(file, filename)

        logger.info(f"Scaling image '{image_name}' by factor {scale_factor}")

        image_processor = Scale.ScaleImage(image_data, image_name, scale_factor)
        result = image_processor.process()

        return image_processing_helper.process_result_with_format(
            result, output_format, "scale image"
        )

    except HTTPException:
        raise  # Re-raise HTTP exceptions as-is
    except Exception as e:
        logger.error(f"Error scaling image: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
