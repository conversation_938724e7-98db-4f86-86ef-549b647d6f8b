import io
import logging
from PIL import Image

from fastapi import APIRouter, Depends, File, UploadFile, Form, HTTPException
from app.utils.file_utils import get_image_bytes_from_storage, get_image_bytes_and_name

from app.api.utils import SimilarImage
from app.services.image_search_service import find_similar_images
from app.api.dependencies import verify_api_key

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/search", tags=["Search"])


@router.post("", dependencies=[Depends(verify_api_key)])
async def search_similar_image(
    file: UploadFile = File(None), filename: str = Form(None), num_results: int = Form(50)
) -> list[SimilarImage]:
    """Searches for similar images to the input image. Provide either a file upload or a filename (not both)."""
    logger.info(f"Received search request with num_results={num_results}")
    image_bytes, _ = await get_image_bytes_and_name(file, filename)
    image = Image.open(io.BytesIO(image_bytes))
    logger.info(f"Searching for similar images with {num_results} results")
    similar_images = find_similar_images(image, num_results)
    logger.info(f"Found {len(similar_images)} similar images")
    return [
        SimilarImage(**img) for img in similar_images
    ]  # If you want to return images, convert to jpeg first
