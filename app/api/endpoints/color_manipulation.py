import logging
from fastapi import APIRouter, Depends, Form, File, UploadFile, HTTPException
from fastapi.responses import FileResponse, JSONResponse

from app.api.utils import Algorithm, ColorReplacements, send_file_response
from app.api.dependencies import verify_api_key
from app.services.cpp_color_limiting import color_limiter
from app.services import replace_colors as Replace
from app.services import extract_colors as Extract
from app.utils.image_processing_helper import image_processing_helper
from app.utils.image_format_converter import convert_to_final_format

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter(tags=["Color Manipulation"])


@router.post("/limit-colors", dependencies=[Depends(verify_api_key)])
async def limit_colors(
    file: UploadFile = File(None),
    filename: str = Form(None),
    num_colors: int = Form(12),
    algorithm: Algorithm = Form(Algorithm.median_cut),
):
    """Limits the number of colors in an image. Provide either a file upload or a filename (not both)."""
    try:
        # Use helper for unified data preparation
        (
            image_data,
            image_name,
            output_format,
        ) = await image_processing_helper.prepare_image_processing(file, filename)

        logger.info(f"Limiting colors in image '{image_name}' to {num_colors} using {algorithm}")

        # Process image (returns PNG bytes for quality preservation)
        result_bytes = color_limiter.limit_colors(image_data, num_colors, algorithm)

        # Convert to final format for response
        final_bytes = convert_to_final_format(result_bytes, output_format)
        return send_file_response(final_bytes, output_format)

    except HTTPException:
        raise  # Re-raise HTTP exceptions as-is
    except Exception as e:
        logger.error(f"Error limiting colors: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/replace-colors", dependencies=[Depends(verify_api_key)])
async def replace_colors(
    file: UploadFile = File(None),
    filename: str = Form(None),
    replacements: ColorReplacements = Form(...),
    texturize: int = Form(1, ge=1, le=256),
) -> FileResponse:
    """Replaces colors in an image. Provide either a file upload or a filename (not both)."""
    try:
        (
            image_data,
            image_name,
            output_format,
        ) = await image_processing_helper.prepare_image_processing(file, filename)

        logger.info(f"Replacing colors in image '{image_name}' with texturize level {texturize}")

        image_processor = Replace.ReplaceColors(
            image_data, replacements.root, texturize, image_name
        )
        result = image_processor.process()

        return image_processing_helper.process_result_with_format(
            result, output_format, "replace colors"
        )

    except HTTPException:
        raise  # Re-raise HTTP exceptions as-is
    except Exception as e:
        logger.error(f"Error replacing colors: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/extract-colors", dependencies=[Depends(verify_api_key)])
async def fetch_colors(
    file: UploadFile = File(None),
    filename: str = Form(None),
) -> list[str]:
    """Fetches all colors in an image. Provide either a file upload or a filename (not both)."""
    try:
        image_data, image_name, _ = await image_processing_helper.prepare_image_processing(
            file, filename
        )

        logger.info(f"Extracting colors from image '{image_name}'")

        image_processor = Extract.ExtractColors(image_data, image_name)
        result = image_processor.process()

        if result.success:
            # Return the list of hex color strings directly as JSON
            return result.data

        return JSONResponse(
            status_code=400,
            content=result.to_dict(),
        )

    except HTTPException:
        raise  # Re-raise HTTP exceptions as-is
    except Exception as e:
        logger.error(f"Error extracting colors: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
