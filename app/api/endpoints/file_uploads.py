import os
import shutil
from fastapi import APIRouter, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
import logging
from fastapi import Depends
from app.api.dependencies import verify_api_key
from app.utils.file_utils import ARTWORK_DIR

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

file_uploads_router = APIRouter(prefix="/files")


@file_uploads_router.post(
    "/upload-artwork/", tags=["File Uploads"], dependencies=[Depends(verify_api_key)]
)
async def upload_artwork_file(file: UploadFile = File(...)):
    """
    Uploads an image file to the /artwork/png directory.
    Only accepts PNG files.
    """
    # Check file extension
    if not file.filename.lower().endswith(".png"):
        raise HTTPException(
            status_code=400, detail="Invalid file type. Only PNG files are allowed."
        )

    # Check content type
    if file.content_type != "image/png":
        raise HTTPException(
            status_code=400,
            detail="Invalid file content type. Only PNG files are allowed.",
        )

    try:
        # Ensure the target directory exists
        os.makedirs(ARTWORK_DIR, exist_ok=True)

        file_path = os.path.join(ARTWORK_DIR, file.filename)

        # Save the file
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        logger.info(f"Successfully uploaded file: {file.filename} to {file_path}")
        return JSONResponse(
            status_code=201,
            content={"message": f"File '{file.filename}' uploaded successfully to {file_path}"},
        )
    except HTTPException as http_exc:
        logger.error(f"HTTPException during file upload: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error uploading file {file.filename}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Could not upload file: {str(e)}")


@file_uploads_router.delete(
    "/delete-artwork/{filename}",
    tags=["File Uploads"],
    dependencies=[Depends(verify_api_key)],
)
async def delete_artwork_file(filename: str):
    """
    Deletes an image file from the /artwork/png directory.
    Only accepts PNG files.
    """
    if not filename.lower().endswith(".png"):
        raise HTTPException(
            status_code=400, detail="Invalid file type. Only PNG files can be deleted."
        )

    file_path = os.path.join(ARTWORK_DIR, filename)

    try:
        if os.path.exists(file_path) and os.path.isfile(file_path):
            os.remove(file_path)
            logger.info(f"Successfully deleted file: {filename}")
            return JSONResponse(
                status_code=200,
                content={"message": f"File '{filename}' deleted successfully."},
            )
        else:
            logger.warning(f"File not found for deletion: {filename}")
            raise HTTPException(status_code=404, detail=f"File '{filename}' not found.")
    except HTTPException as http_exc:
        logger.error(f"HTTPException during file deletion: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error deleting file {filename}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Could not delete file: {str(e)}")


@file_uploads_router.get(
    "/list-artwork/", tags=["File Uploads"], dependencies=[Depends(verify_api_key)]
)
async def list_artwork_files():
    """
    Lists all PNG files in the /artwork/png directory.
    """
    try:
        if not os.path.exists(ARTWORK_DIR):
            return JSONResponse(status_code=200, content={"files": []})
        files = [
            f
            for f in os.listdir(ARTWORK_DIR)
            if f.lower().endswith(".png") and os.path.isfile(os.path.join(ARTWORK_DIR, f))
        ]
        return JSONResponse(status_code=200, content={"files": files})
    except Exception as e:
        logger.error(f"Error listing files in {ARTWORK_DIR}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Could not list files: {str(e)}")
