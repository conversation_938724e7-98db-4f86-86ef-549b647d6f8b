# Renamed from image_pipeline_router.py
import logging
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Body
from fastapi.responses import FileResponse
from pydantic import BaseModel

from app.api.dependencies import verify_api_key
from app.utils.image_processing_helper import image_processing_helper
from app.utils.performance_cache import performance_monitor, track_performance

from app.services.cpp_color_limiting import color_limiter
from app.services import replace_colors as Replace
from app.services import extract_colors as Extract
from app.services.adjust_hue_saturation import AdjustHueSaturation
from app.services.convert_to_grayscale import ConvertToGrayscale
from app.services.invert_colors import InvertColors
from app.services.scale_image import ScaleImage
from app.services.tech_pack_service import TechPackService
from app.api.utils import Algorithm, send_file_response

logger = logging.getLogger(__name__)


class ProcessingStep(BaseModel):
    endpoint_name: str
    params: Dict[str, Any]


class PipelineRequest(BaseModel):
    operations: List[ProcessingStep]
    tech_pack: bool = False


final_image_router = APIRouter(tags=["Final Image Creation"])


@track_performance("dispatch_operation")
async def dispatch_operation(
    image_bytes: bytes, operation: ProcessingStep, filename: str
) -> bytes:
    """
    Optimized operation dispatcher with session-based caching and performance tracking.
    """
    op = operation.endpoint_name
    params = operation.params

    # Check for cached result (session-only)
    cached_result = performance_monitor.get_cached_result(op, image_bytes, params)
    if cached_result:
        logger.debug(f"Using cached result for {op}")
        return cached_result

    # Execute operation
    result = None
    if op == "limit-colors":
        num_colors = params.get("num_colors", 12)
        algorithm = Algorithm(params.get("algorithm", "median_cut"))
        result = color_limiter.limit_colors(image_bytes, num_colors, algorithm)
    elif op == "replace-colors":
        replacements = params.get("replacements", {}).get("root")
        if not replacements:
            raise HTTPException(
                status_code=400,
                detail="'replacements.root' is required for replace-colors",
            )
        texturize = params.get("texturize", 1)
        operation_result = Replace.ReplaceColors(
            image_bytes, replacements, texturize, filename
        ).process()
        if not operation_result.success:
            raise HTTPException(
                status_code=500,
                detail=f"replace-colors failed: {operation_result.message}",
            )
        result = operation_result.data
    elif op == "extract-colors":
        Extract.ExtractColors(image_bytes, filename).process()
        result = image_bytes  # This operation doesn't modify the image
    elif op == "adjust-hue-saturation":
        hue = params.get("hue", 0.0)
        saturation = params.get("saturation", 1.0)
        operation_result = AdjustHueSaturation(
            image_bytes, filename, hue, saturation
        ).process()
        if not operation_result.success:
            raise HTTPException(
                status_code=500,
                detail=f"adjust-hue-saturation failed: {operation_result.message}",
            )
        result = operation_result.data
    elif op == "convert-to-grayscale":
        operation_result = ConvertToGrayscale(image_bytes, filename).process()
        if not operation_result.success:
            raise HTTPException(
                status_code=500,
                detail=f"convert-to-grayscale failed: {operation_result.message}",
            )
        result = operation_result.data
    elif op == "invert-colors":
        operation_result = InvertColors(image_bytes, filename).process()
        if not operation_result.success:
            raise HTTPException(
                status_code=500,
                detail=f"invert-colors failed: {operation_result.message}",
            )
        result = operation_result.data
    elif op == "scale-image":
        scale_factor = params.get("scale_factor", 1.0)
        operation_result = ScaleImage(image_bytes, filename, scale_factor).process()
        if not operation_result.success:
            raise HTTPException(
                status_code=500,
                detail=f"scale-image failed: {operation_result.message}",
            )
        result = operation_result.data
    else:
        raise HTTPException(status_code=400, detail=f"Unknown endpoint_name: {op}")

    # Cache the result (session-only)
    if result:
        performance_monitor.cache_result(op, image_bytes, params, result)

    return result


@final_image_router.post(
    "/generate-final-output/{filename}",
    response_class=FileResponse,
    dependencies=[Depends(verify_api_key)],
)
@track_performance("generate_final_output")
async def generate_final_output(filename: str, request: PipelineRequest = Body(...)):
    """
    Apply a sequence of image operations to a PNG in /artwork/png and return the final PNG as a file response.
    If tech_pack is True, returns a zip file with PSD, PDF with artwork info, and cropped PDF for 11x17 view.
    Optimized version with performance tracking and better error handling.
    """
    try:
        # Use optimized async file loading
        image_bytes, _, _ = await image_processing_helper.prepare_image_processing(
            None, filename
        )

        # Apply all operations in sequence with performance tracking
        operation_count = len(request.operations)
        logger.info(f"Processing {operation_count} operations for {filename}")

        for i, op in enumerate(request.operations):
            logger.debug(
                f"Executing operation {i+1}/{operation_count}: {op.endpoint_name}"
            )
            image_bytes = await dispatch_operation(image_bytes, op, filename)

        # Check if tech pack should be generated
        if request.tech_pack:
            logger.info(f"Generating tech pack for {filename}")
            tech_pack_service = TechPackService()
            zip_bytes = tech_pack_service.create_tech_pack(image_bytes, filename)
            return send_file_response(zip_bytes, "zip")
        else:
            return send_file_response(image_bytes, "png")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in pipeline for {filename}: {str(e)}")
        # Clear cache for this session on error to prevent bad state
        performance_monitor.clear_cache()
        raise HTTPException(
            status_code=500, detail=f"Could not process image pipeline: {str(e)}"
        )


@final_image_router.get("/performance-stats", dependencies=[Depends(verify_api_key)])
async def get_performance_stats():
    """
    Get performance statistics for monitoring optimization effectiveness.
    """
    stats = performance_monitor.get_performance_stats()
    return {
        "performance_stats": stats,
        "cache_info": {
            "current_cache_size": len(performance_monitor._operation_cache),
            "max_cache_size": performance_monitor._max_cache_size,
        },
        "optimizations_active": [
            "Session-based operation caching",
            "Async file I/O with aiofiles",
            "Optimized image format conversion",
            "Fast PNG compression (level 1)",
            "Progressive JPEG encoding",
            "File metadata caching",
            "Performance tracking and logging",
        ],
    }


@final_image_router.post("/clear-cache", dependencies=[Depends(verify_api_key)])
async def clear_performance_cache():
    """
    Clear the performance cache (for testing or troubleshooting).
    """
    performance_monitor.clear_cache()
    return {"message": "Performance cache cleared successfully"}
