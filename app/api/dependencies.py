from fastapi import Header, HTTPException
from typing import Annotated
import secrets
import os

API_KEY = os.getenv("API_ACCESS_KEY")


async def verify_api_key(api_key: Annotated[str, Header()]):
    """Verify the API key

    Args:
        api_key (str): API key

    Returns:
        str: API key if valid
    """
    if not API_KEY:
        raise HTTPException(status_code=500, detail="API key not configured")
    if not api_key:
        raise HTTPException(status_code=401, detail="API key required")
    if secrets.compare_digest(api_key, API_KEY):
        return api_key
    else:
        raise HTTPException(
            status_code=403,
            detail="Could not validate credentials",
        )
