import time
import modal
import io
import os
import tempfile
from fastapi import Depends, File, UploadFile, HTTPException, status
from fastapi.responses import FileResponse
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from PIL import Image

MODEL_REPO_ID = "mattmdjaga/segformer_b2_clothes"
MODEL_DIR = "/cache"

app = modal.App("cloth-detection")
cache_volume = modal.Volume.from_name("hf-hub-cache", create_if_missing=True)
image = (
    modal.Image.debian_slim(python_version="3.12")
    .pip_install(
        "huggingface-hub==0.27.1",
        "timm",
        "transformers==4.47.1",
        "fastapi[standard]==0.115.6",
        "Pillow==11.1.0",
    )
    .env({"HF_HUB_CACHE": MODEL_DIR})
)

# Specifies the Python packages and modules to import
# for the `modal.Image` instance named `image`.
with image.imports():
    import torch
    import numpy as np
    from transformers import SegformerImageProcessor, SegformerForSemanticSegmentation

    # from huggingface_hub import snapshot_download

# # Download the model (used only once)
# @app.function(image=image, volumes={MODEL_DIR: cache_volume})
# def download_model():
#     loc = snapshot_download(repo_id=MODEL_REPO_ID)
#     print(f"Saved model to {loc}")

auth_scheme = HTTPBearer()


@app.cls(
    image=image,
    volumes={MODEL_DIR: cache_volume},
    gpu="T4",
    # cpu=1,
    secrets=[modal.Secret.from_name("mixmix-middleware")],
    # keep_warm=True,
)
class ObjectDetection:
    @modal.web_endpoint(method="POST")
    async def run_analysis(
        self,
        file: UploadFile = File(...),
        token: HTTPAuthorizationCredentials = Depends(auth_scheme),
    ):
        """Runs the clothing detection and masking process.

        This endpoint receives an image file, loads the model, detects clothing,
        creates a mask, and returns the masked image.

        Args:
            file (UploadFile): The image file to process.
            token (HTTPAuthorizationCredentials): The bearer token for authentication.

        Raises:
            HTTPException: If the bearer token is incorrect.

        Returns:
            FileResponse: The masked image as a file response.
        """

        # FIXME - This should be replaced with a more secure method
        if token.credentials != os.environ["MODAL_KEY"]:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect bearer token",
                headers={"WWW-Authenticate": "Bearer"},
            )

        image = await file.read()
        image = Image.open(io.BytesIO(image))
        self.image = image

        start = time.time()
        self.load_model.local()
        print("Model loaded in", time.time() - start, "seconds")

        start = time.time()
        self.detect_cloth.local()
        print("Cloth detected in", time.time() - start, "seconds")

        start = time.time()
        self.mask_cloth.local()
        print("Cloth masked in", time.time() - start, "seconds")

        return self.send_result.local()

    @modal.method()
    def load_model(self):
        """Loads the pre-trained segmentation model and feature extractor.

        This method initializes the feature extractor, model, and device,
        then moves the model to the appropriate device (GPU if available, otherwise CPU).
        """
        self.feature_extractor = SegformerImageProcessor.from_pretrained(MODEL_REPO_ID)
        self.model = SegformerForSemanticSegmentation.from_pretrained(MODEL_REPO_ID)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model.to(self.device)
        print("Model loaded successfully on", self.device)

    @modal.method()
    def detect_cloth(self):
        """Detects clothing in the image and creates a mask.

        This method preprocesses the image, runs it through the model, and
        generates a mask for the clothing in the image.
        """
        image = self.image
        inputs = self.feature_extractor(image, return_tensors="pt").to(self.device)
        with torch.no_grad():
            outputs = self.model(**inputs)

        logits = outputs.logits
        upsampled_logits = torch.nn.functional.interpolate(
            logits, size=image.size[::-1], mode="bilinear", align_corners=False
        )
        self.predicted_masks = upsampled_logits.argmax(dim=1)[0].cpu()

    @modal.method()
    def mask_cloth(self):
        """Creates a mask for the clothing region in the image.

        This method generates a mask for the clothing region based on the predicted masks.
        It prioritizes the dress region (mask value 7) and falls back to upper clothes, pants, and skirts.
        If less than 30% of the image is covered by ones, it returns the original image.
        """
        masks = np.array(self.predicted_masks)
        dress_mask = np.zeros_like(masks, dtype=float)

        if 7 in masks:  # Prioritize mask value 7 - Dress
            dress_mask[masks == 7] = 1.0
        else:  # Upper clothes, pants, skirts
            dress_mask[np.isin(masks, [4, 5, 6])] = 1.0

        # if less than 10% of the image is covered by ones, return the original image
        # FIXME - This threshold can be adjusted based on future results
        if np.sum(dress_mask) / dress_mask.size < 0.1:
            self.dress_region = np.array(self.image)
            return

        self.dress_region = dress_mask[:, :, None] * np.array(self.image)

    @modal.method()
    def send_result(self):
        """Saves the masked image to a temporary file and returns the file response.

        This method saves the masked image as a temporary file and returns the file response.
        The file is automatically deleted after the response is sent.

        Returns:
            FileResponse: The masked image as a file response.
        """
        with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as tmp_file:
            cloth_image = Image.fromarray(self.dress_region.astype(np.uint8))
            cloth_image.save(tmp_file.name)
            return FileResponse(tmp_file.name, background=lambda: os.unlink(tmp_file.name))
