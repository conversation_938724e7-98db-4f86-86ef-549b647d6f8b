from app.utils.logging import Logger
from app.utils.image_format_converter import image_format_converter


class ImageProcessor:
    def __init__(self):
        self.logger = Logger(self.__class__.__name__)

    def get_image_in_bytes(self, image_data: bytes) -> bytes:
        """Convert the image data to bytes."""
        # Ensure image_data is bytes, not BytesIO
        if hasattr(image_data, "read"):
            return image_data.read()
        return image_data

    def get_image_format(self, filename: str) -> str:
        """Get the format of the uploaded image file."""
        if not filename:
            return "jpeg"  # Default format

        ext = filename.split(".")[-1].lower()

        # Check if the format is supported by our converter
        supported_formats = image_format_converter.get_supported_formats()
        if ext not in supported_formats:
            self.logger.warning(f"Unsupported image format: {ext}. Defaulting to jpeg.")
            return "jpeg"

        # Normalize jpg to jpeg for consistency
        return "jpeg" if ext == "jpg" else ext

    def convert_image_format(self, image_bytes: bytes, target_format: str) -> bytes:
        """Convert image bytes to the specified format using the centralized converter."""
        try:
            return image_format_converter.convert_bytes_to_format(image_bytes, target_format)
        except Exception as e:
            self.logger.error(f"Failed to convert image to {target_format}: {str(e)}")
            raise

    def get_mime_type(self, format_name: str) -> str:
        """Get the MIME type for a format."""
        try:
            return image_format_converter.get_mime_type(format_name)
        except ValueError as e:
            self.logger.warning(f"Unknown format {format_name}, defaulting to JPEG MIME type")
            return "image/jpeg"

    def process(self):
        """This method should be implemented by the child class."""
        raise NotImplementedError("process method not implemented")
