from dataclasses import dataclass
from typing import <PERSON><PERSON>, <PERSON>V<PERSON>, Dict, Any, Optional

T = TypeVar("T")  # Type parameter for success data


@dataclass
class FuncResponse(Generic[T]):
    """A result that can represent either success or failure."""

    success: bool

    # Private attributes - use properties to access
    _data: Optional[T] = None
    _error_message: Optional[str] = None
    _source: str = ""
    _error_code: Optional[int] = None
    _details: Dict[str, Any] = None

    @classmethod
    def success(cls, data: T, source: str, details: Dict[str, Any] = None) -> "FuncResponse[T]":
        """Create a success result with data."""
        return cls(success=True, _data=data, _source=source, _details=details or {})

    @classmethod
    def failure(
        cls,
        error_message: str,
        source: str,
        error_code: int = None,
        details: Dict[str, Any] = None,
    ) -> "FuncResponse[T]":
        """Create a failure result with error details."""
        return cls(
            success=False,
            _error_message=error_message,
            _source=source,
            _error_code=error_code,
            _details=details or {},
        )

    @property
    def data(self) -> T:
        """Get the success data."""
        if not self.success:
            raise ValueError("Cannot access data on failure result")
        return self._data

    @property
    def error_message(self) -> str:
        """Get the error message."""
        if self.success:
            raise ValueError("Cannot access error_message on success result")
        return self._error_message

    @property
    def source(self) -> Optional[str]:
        """Get the source of the error if available."""
        return self._source

    @property
    def error_code(self) -> Optional[int]:
        """Get the error code if available."""
        if self.success:
            raise ValueError("Cannot access error_code on success result")
        return self._error_code

    @property
    def details(self) -> Dict[str, Any]:
        """Get additional details for either success or failure."""
        return self._details or {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        if self.success:
            result = {"success": True, "data": self._data, "details": self._details}
        else:
            result = {
                "success": False,
                "error": {
                    "message": self._error_message,
                    "source": self._source,
                    "code": self._error_code,
                    "details": self._details,
                },
            }
        return {k: v for k, v in result.items() if v is not None}
