from typing import Protocol, Callable, Any, Optional
from functools import wraps
import time
import logging


class LoggerProtocol(Protocol):
    def info(self, message: str) -> None:
        ...

    def error(self, message: str) -> None:
        ...

    def warning(self, message: str) -> None:
        ...

    def debug(self, message: str) -> None:
        ...


class Logger:
    """Provides logging functionality."""

    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)

        handler = logging.StreamHandler()
        handler.setLevel(logging.INFO)

        formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

    def info(self, message: str) -> None:
        self.logger.info(message)

    def error(self, message: str) -> None:
        self.logger.error(message)

    def warning(self, message: str) -> None:
        self.logger.warning(message)

    def debug(self, message: str) -> None:
        self.logger.debug(message)


def log_time(func):
    """The decorator logs the time taken for a function to execute

    Args:
        func (function): The function to be decorated

    Returns:
        function: The wrapped function
    """

    def wrapper(*args, **kwargs):
        print(f"Calling {func.__name__}")
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"{func.__name__} took {end_time - start_time:.2f} seconds")
        return result

    return wrapper
