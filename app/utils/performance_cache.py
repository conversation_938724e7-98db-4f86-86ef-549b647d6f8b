"""
Performance monitoring and optimization utilities for image processing.
Provides lightweight caching and performance tracking without violating requirements.
"""

import hashlib
import logging
import time
from functools import wraps
from typing import Dict, Any, Optional, Tuple
import weakref

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """Lightweight performance monitoring without persistent caching."""

    def __init__(self):
        # Use weak references to allow garbage collection
        self._operation_cache: Dict[str, Any] = {}
        self._performance_stats: Dict[str, list] = {}
        self._max_cache_size = 100  # Limit memory usage

    def _generate_cache_key(self, operation: str, image_hash: str, params: Dict[str, Any]) -> str:
        """Generate a cache key for operation results."""
        param_str = str(sorted(params.items())) if params else ""
        return f"{operation}:{image_hash}:{hashlib.md5(param_str.encode()).hexdigest()[:8]}"

    def _get_image_hash(self, image_data: bytes) -> str:
        """Generate a lightweight hash of image data for caching."""
        # Use only first and last 1KB for speed
        sample_size = min(1024, len(image_data) // 2)
        sample = image_data[:sample_size] + image_data[-sample_size:]
        return hashlib.md5(sample).hexdigest()[:12]

    def get_cached_result(self, operation: str, image_data: bytes, params: Dict[str, Any] = None) -> Optional[bytes]:
        """Get cached result if available (session-only, no persistence)."""
        if len(self._operation_cache) > self._max_cache_size:
            # Clear oldest entries when cache is full
            keys_to_remove = list(self._operation_cache.keys())[:20]
            for key in keys_to_remove:
                self._operation_cache.pop(key, None)

        image_hash = self._get_image_hash(image_data)
        cache_key = self._generate_cache_key(operation, image_hash, params or {})
        return self._operation_cache.get(cache_key)

    def cache_result(self, operation: str, image_data: bytes, params: Dict[str, Any] = None, result: bytes = None):
        """Cache operation result (session-only)."""
        if result and len(result) < 50 * 1024 * 1024:  # Only cache results under 50MB
            image_hash = self._get_image_hash(image_data)
            cache_key = self._generate_cache_key(operation, image_hash, params or {})
            self._operation_cache[cache_key] = result

    def track_performance(self, operation: str, duration: float):
        """Track performance statistics."""
        if operation not in self._performance_stats:
            self._performance_stats[operation] = []

        # Keep only last 100 measurements per operation
        self._performance_stats[operation].append(duration)
        if len(self._performance_stats[operation]) > 100:
            self._performance_stats[operation] = self._performance_stats[operation][-100:]

    def get_performance_stats(self) -> Dict[str, Dict[str, float]]:
        """Get performance statistics."""
        stats = {}
        for operation, times in self._performance_stats.items():
            if times:
                stats[operation] = {
                    'avg_time': sum(times) / len(times),
                    'min_time': min(times),
                    'max_time': max(times),
                    'call_count': len(times)
                }
        return stats

    def clear_cache(self):
        """Clear the session cache."""
        self._operation_cache.clear()

# Global instance for the session
performance_monitor = PerformanceMonitor()

def track_performance(operation_name: str):
    """Decorator to track operation performance."""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                performance_monitor.track_performance(operation_name, duration)
                if duration > 1.0:  # Log slow operations
                    logger.warning(f"Slow operation {operation_name}: {duration:.2f}s")

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                performance_monitor.track_performance(operation_name, duration)
                if duration > 1.0:  # Log slow operations
                    logger.warning(f"Slow operation {operation_name}: {duration:.2f}s")

        # Return appropriate wrapper based on function type
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator
