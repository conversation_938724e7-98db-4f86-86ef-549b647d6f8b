"""
Basic validation utilities for image processing operations.

This module provides minimal validation for image processing.
"""

import logging
from typing import <PERSON>ple, Optional

logger = logging.getLogger(__name__)


class ImageValidator:
    """
    Simple image validation utility.

    Only performs basic empty data checks.
    """

    @classmethod
    def validate_image_bytes(cls, image_bytes: bytes) -> Tuple[bool, Optional[str]]:
        """
        Validate image bytes for basic requirements.

        Args:
            image_bytes: Image data as bytes

        Returns:
            Tuple of (is_valid, error_message)
        """
        if not image_bytes:
            return False, "Empty image data provided"

        return True, None


# Global validator instance
image_validator = ImageValidator()
