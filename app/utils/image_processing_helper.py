"""
Unified image processing utilities following SOLID principles.

This module provides common functionality for image processing endpoints
to reduce code duplication and improve maintainability.
"""

import logging
from typing import Tuple, Any
from fastapi import UploadFile, HTTPException
from fastapi.responses import JSONResponse

from app.utils.file_utils import get_image_bytes_and_name, determine_output_format
from app.utils.image_format_converter import convert_to_final_format
from app.api.utils import send_file_response

logger = logging.getLogger(__name__)


class ImageProcessingHelper:
    """
    Helper class for common image processing operations.

    Follows the Single Responsibility Principle by providing a focused
    set of utilities for image processing endpoints.
    """

    @staticmethod
    async def prepare_image_processing(
        file: UploadFile = None, filename: str = None
    ) -> Tuple[bytes, str, str]:
        """
        Prepare image data and determine output format.

        Args:
            file: Optional uploaded file
            filename: Optional filename from storage

        Returns:
            Tuple of (image_data, image_name, output_format)

        Raises:
            HTTPException: For validation or processing errors
        """
        try:
            image_data, image_name = await get_image_bytes_and_name(file, filename)
            output_format = determine_output_format(file, filename)
            return image_data, image_name, output_format
        except HTTPException:
            raise  # Re-raise HTTP exceptions as-is
        except Exception as e:
            logger.error(f"Error preparing image processing: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to prepare image: {str(e)}")

    @staticmethod
    def process_result_with_format(
        result: Any, output_format: str, operation_name: str = "operation"
    ):
        """
        Process service result and convert to final format.

        Args:
            result: Service processing result
            output_format: Target output format
            operation_name: Name of operation for logging

        Returns:
            FileResponse or JSONResponse based on result success

        Raises:
            HTTPException: For processing errors
        """
        try:
            if result.success:
                # Convert from PNG (internal processing) to final format for output
                final_data = convert_to_final_format(result.data, output_format)
                return send_file_response(final_data, output_format)

            return JSONResponse(
                status_code=400,
                content=result.to_dict(),
            )
        except Exception as e:
            logger.error(f"Error processing {operation_name} result: {str(e)}")
            raise HTTPException(
                status_code=500, detail=f"Failed to process {operation_name}: {str(e)}"
            )

    @staticmethod
    def handle_endpoint_errors(operation_name: str):
        """
        Decorator for consistent error handling in endpoints.

        Args:
            operation_name: Name of the operation for logging
        """

        def decorator(func):
            async def wrapper(*args, **kwargs):
                try:
                    return await func(*args, **kwargs)
                except HTTPException:
                    raise  # Re-raise HTTP exceptions as-is
                except Exception as e:
                    logger.error(f"Error in {operation_name}: {str(e)}")
                    raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

            return wrapper

        return decorator


# Singleton instance for use throughout the application
image_processing_helper = ImageProcessingHelper()
