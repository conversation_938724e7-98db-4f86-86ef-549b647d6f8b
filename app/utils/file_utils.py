import os
from fastapi import <PERSON><PERSON>P<PERSON>xception, UploadFile
import asyncio
import aiofiles
from functools import lru_cache

ARTWORK_DIR = "/artwork/png"


# File metadata cache to avoid redundant OS calls
@lru_cache(maxsize=1000)
def _get_file_info(file_path: str) -> tuple[bool, int]:
    """Cache file existence and size information."""
    try:
        stat = os.stat(file_path)
        return True, stat.st_size
    except (FileNotFoundError, OSError):
        return False, 0


def get_image_bytes_from_storage(filename: str) -> bytes:
    """
    Fetches the image bytes from the /artwork/png/ storage volume.
    Raises HTTPException if the file does not exist or is not a PNG.
    """
    if not filename.lower().endswith(".png"):
        raise HTTPException(status_code=400, detail="Only PNG files are supported.")
    if ".." in filename or filename.startswith("/"):
        raise HTTPException(status_code=400, detail="Invalid filename.")

    file_path = os.path.join(ARTWORK_DIR, filename)
    if not os.path.exists(file_path) or not os.path.isfile(file_path):
        raise HTTPException(status_code=404, detail=f"File '{filename}' not found in storage.")

    try:
        # Use buffered reading for better performance
        with open(file_path, "rb", buffering=8192) as f:
            return f.read()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error reading file: {str(e)}")


async def get_image_bytes_from_storage_async(filename: str) -> bytes:
    """
    Async version of file loading for better performance in FastAPI.
    """
    if not filename.lower().endswith(".png"):
        raise HTTPException(status_code=400, detail="Only PNG files are supported.")
    if ".." in filename or filename.startswith("/"):
        raise HTTPException(status_code=400, detail="Invalid filename.")

    file_path = os.path.join(ARTWORK_DIR, filename)
    exists, file_size = _get_file_info(file_path)

    if not exists:
        raise HTTPException(status_code=404, detail=f"File '{filename}' not found in storage.")

    try:
        async with aiofiles.open(file_path, "rb") as f:
            return await f.read()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error reading file: {str(e)}")


def get_image_bytes_and_name_sync(file: UploadFile = None, filename: str = None):
    """
    Utility to get image bytes and name from either an UploadFile or a filename.
    Ensures exactly one is provided, otherwise raises HTTPException.
    This is a sync version for use in sync context (not recommended for FastAPI endpoints).
    """
    # Normalize empty or whitespace-only filename to None
    if filename is not None and not filename.strip():
        filename = None

    if (file is None and filename is None) or (file is not None and filename is not None):
        raise HTTPException(
            status_code=400, detail="Provide either a file or a filename, not both."
        )

    if file is not None:
        if not hasattr(file, "file") or file.file is None:
            raise HTTPException(status_code=400, detail="Invalid file upload")
        try:
            image_data = asyncio.run(file.read())
            image_name = file.filename or "uploaded_file"
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error reading uploaded file: {str(e)}")
    else:
        image_data = get_image_bytes_from_storage(filename)
        image_name = filename
    return image_data, image_name


async def get_image_bytes_and_name(file: UploadFile = None, filename: str = None):
    """
    Utility to get image bytes and name from either an UploadFile or a filename.
    Ensures exactly one is provided, otherwise raises HTTPException.
    Async version for use in FastAPI endpoints.
    """
    # Normalize empty or whitespace-only filename to None
    if filename is not None and not filename.strip():
        filename = None

    if (file is None and filename is None) or (file is not None and filename is not None):
        raise HTTPException(
            status_code=400, detail="Provide either a file or a filename, not both."
        )

    if file is not None:
        if not hasattr(file, "file") or file.file is None:
            raise HTTPException(status_code=400, detail="Invalid file upload")

        try:
            image_data = await file.read()
            if not image_data:
                raise HTTPException(status_code=400, detail="Empty file uploaded")
            image_name = file.filename or "uploaded_file"
        except HTTPException:
            raise  # Re-raise HTTP exceptions
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error reading uploaded file: {str(e)}")
    else:
        # Use async file loading for better performance
        image_data = await get_image_bytes_from_storage_async(filename)
        image_name = filename

    return image_data, image_name


def save_img(image, path):
    """The function saves an 'image' to a specified path and prints a message confirmation."""
    image.save(path)
    print(f"Image saved to {path}")


def save_file(image, path):
    """The function saves a 'file' to a specified path and prints a message confirmation."""
    with open(path, "wb") as f:
        f.write(image)
    print(f"File saved to {path}")


def get_format_from_filename(filename: str) -> str:
    """
    Extract format from filename extension with robust validation.
    Returns the format (jpeg, png) or raises exception for unsupported formats.
    """
    if not filename or not isinstance(filename, str):
        return "jpeg"  # Default fallback

    # Strip any path components and get just the filename
    filename = os.path.basename(filename.strip())

    if not filename or "." not in filename:
        return "jpeg"  # Default for files without extension

    extension = filename.lower().split(".")[-1]

    # Supported formats mapping
    format_mapping = {"jpg": "jpeg", "jpeg": "jpeg", "png": "png"}

    return format_mapping.get(extension, "jpeg")  # Default to jpeg for unsupported


def determine_output_format(file: UploadFile = None, filename: str = None) -> str:
    """
    Determine the output format based on the input:
    - If file is uploaded: use the file's extension format
    - If filename is provided: default to JPEG (since files in storage are PNG but we want JPEG output)

    Returns format string: 'jpeg' or 'png'
    """
    if file is not None and file.filename:
        # File upload: use the original file's format
        return get_format_from_filename(file.filename)
    else:
        # Filename only: default to JPEG
        return "jpeg"
