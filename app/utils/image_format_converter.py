"""
Centralized image format conversion utility following SOLID principles.

This module provides a single responsibility for converting image bytes between different formats,
supporting the Open/Closed principle through extensible format handlers.
"""

import io
from abc import ABC, abstractmethod
from typing import Dict
from PIL import Image
from app.utils.logging import log_time


class FormatHandler(ABC):
    """Abstract base class for image format handlers."""

    @abstractmethod
    def encode(self, image: Image.Image) -> bytes:
        """Encode a PIL Image to bytes in the specific format."""
        pass

    @property
    @abstractmethod
    def mime_type(self) -> str:
        """Return the MIME type for this format."""
        pass

    @property
    @abstractmethod
    def file_extension(self) -> str:
        """Return the file extension for this format."""
        pass


class JPEGHandler(FormatHandler):
    """Handler for JPEG format with high quality settings."""

    def encode(self, image: Image.Image) -> bytes:
        # Convert to RGB if needed (JPEG doesn't support transparency)
        if image.mode in ("RGBA", "LA"):
            # Create white background for transparency
            background = Image.new("RGB", image.size, (255, 255, 255))
            if image.mode == "RGBA":
                background.paste(image, mask=image.split()[-1])  # Use alpha channel as mask
            else:
                background.paste(image)
            image.close()  # Clean up original image
            image = background
        elif image.mode == "L":
            # Convert grayscale to RGB for consistent output
            old_image = image
            image = image.convert("RGB")
            if old_image is not image:
                old_image.close()  # Clean up if new image was created
        elif image.mode not in ("RGB",):
            old_image = image
            image = image.convert("RGB")
            if old_image is not image:
                old_image.close()  # Clean up if new image was created

        try:
            buffer = io.BytesIO()
            image.save(buffer, format="JPEG", quality=95, optimize=True)
            return buffer.getvalue()
        finally:
            buffer.close()  # Ensure buffer is closed

    @property
    def mime_type(self) -> str:
        return "image/jpeg"

    @property
    def file_extension(self) -> str:
        return ".jpg"


class PNGHandler(FormatHandler):
    """Handler for PNG format with optimized compression."""

    def encode(self, image: Image.Image) -> bytes:
        try:
            buffer = io.BytesIO()
            image.save(buffer, format="PNG", optimize=False, compress_level=0)
            return buffer.getvalue()
        finally:
            buffer.close()  # Ensure buffer is closed

    @property
    def mime_type(self) -> str:
        return "image/png"

    @property
    def file_extension(self) -> str:
        return ".png"


class ImageFormatConverter:
    """
    Centralized image format converter following SOLID principles.

    This class provides a single responsibility for format conversion,
    supports the Open/Closed principle through registrable format handlers,
    and follows the Dependency Inversion principle by depending on abstractions.
    """

    def __init__(self):
        """Initialize the converter with default format handlers."""
        self._handlers: Dict[str, FormatHandler] = {
            "jpeg": JPEGHandler(),
            "jpg": JPEGHandler(),  # Alias for JPEG
            "png": PNGHandler(),
        }
        self._default_format = "jpeg"

    def register_handler(self, format_name: str, handler: FormatHandler) -> None:
        """
        Register a new format handler.

        Args:
            format_name: The format name (e.g., 'jpeg', 'png')
            handler: The format handler instance
        """
        self._handlers[format_name.lower()] = handler

    def get_supported_formats(self) -> list[str]:
        """Get a list of supported format names."""
        return list(self._handlers.keys())

    def get_mime_type(self, format_name: str) -> str:
        """
        Get the MIME type for a format.

        Args:
            format_name: The format name

        Returns:
            The MIME type string

        Raises:
            ValueError: If the format is not supported
        """
        handler = self._get_handler(format_name)
        return handler.mime_type

    def get_file_extension(self, format_name: str) -> str:
        """
        Get the file extension for a format.

        Args:
            format_name: The format name

        Returns:
            The file extension string (including the dot)

        Raises:
            ValueError: If the format is not supported
        """
        handler = self._get_handler(format_name)
        return handler.file_extension

    @log_time
    def convert_bytes_to_format(self, image_bytes: bytes, target_format: str) -> bytes:
        """
        Convert image bytes to the specified format with proper resource management.

        Args:
            image_bytes: Input image as bytes
            target_format: Target format name (e.g., 'jpeg', 'png')

        Returns:
            Image bytes in the target format

        Raises:
            ValueError: If the target format is not supported
            Exception: If the conversion fails
        """
        if not image_bytes:
            raise ValueError("Empty image data provided")

        # Quick format detection to avoid unnecessary conversion
        if self._is_already_target_format(image_bytes, target_format):
            return image_bytes

        image = None
        buffer = None
        try:
            # Decode the input image with optimized buffer
            buffer = io.BytesIO(image_bytes)
            image = Image.open(buffer)
            image.load()  # Force load to avoid lazy loading issues

            # Get the appropriate handler and encode
            handler = self._get_handler(target_format)
            return handler.encode(image)

        except Exception as e:
            raise Exception(f"Failed to convert image to {target_format}: {str(e)}")
        finally:
            # Clean up resources
            if image:
                image.close()
            if buffer:
                buffer.close()

    def _is_already_target_format(self, image_bytes: bytes, target_format: str) -> bool:
        """Quick check if image is already in target format."""
        if len(image_bytes) < 12:
            return False

        # Check common format signatures
        if target_format.lower() in ("jpeg", "jpg"):
            return image_bytes.startswith(b"\xff\xd8\xff")
        elif target_format.lower() == "png":
            return image_bytes.startswith(b"\x89PNG\r\n\x1a\n")

        return False

    @log_time
    def convert_pil_to_format(self, image: Image.Image, target_format: str) -> bytes:
        """
        Convert a PIL Image to the specified format.

        Args:
            image: PIL Image object
            target_format: Target format name (e.g., 'jpeg', 'png')

        Returns:
            Image bytes in the target format

        Raises:
            ValueError: If the target format is not supported
            Exception: If the conversion fails
        """
        if not image:
            raise ValueError("Invalid image object provided")

        try:
            handler = self._get_handler(target_format)
            return handler.encode(image)

        except Exception as e:
            raise Exception(f"Failed to convert PIL image to {target_format}: {str(e)}")

    def _get_handler(self, format_name: str) -> FormatHandler:
        """
        Get the handler for a format.

        Args:
            format_name: The format name

        Returns:
            The format handler

        Raises:
            ValueError: If the format is not supported
        """
        format_name_lower = format_name.lower()
        if format_name_lower not in self._handlers:
            supported = ", ".join(self.get_supported_formats())
            raise ValueError(f"Unsupported format '{format_name}'. Supported formats: {supported}")

        return self._handlers[format_name_lower]


# Global instance for use throughout the application
image_format_converter = ImageFormatConverter()


def convert_to_final_format(image_bytes: bytes, target_format: str = "jpeg") -> bytes:
    """
    Convert image bytes to the final output format with validation and optimization.

    This function should be used for final format conversion before sending response,
    not during intermediate processing steps in a chain of modifications.

    Args:
        image_bytes: Input image as bytes
        target_format: Target format ('jpeg', 'jpg', or 'png')

    Returns:
        Image bytes in the target format

    Raises:
        ValueError: If the target format is not supported or input is invalid
        Exception: If the conversion fails
    """
    if not image_bytes:
        raise ValueError("Empty image data provided for conversion")

    if not isinstance(target_format, str):
        raise ValueError("Target format must be a string")

    # Normalize format
    target_format = target_format.lower().strip()
    if target_format not in {"jpeg", "jpg", "png"}:
        raise ValueError(f"Unsupported target format: {target_format}")

    return image_format_converter.convert_bytes_to_format(image_bytes, target_format)
