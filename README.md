# mixmix-python-middleware
This repository provides a Python middleware for handling lab actions, AI integrations, and other advanced image processing tasks. It uses FastAPI for serving the endpoints and interacts with external services and custom C++ modules for high-performance operations.

## Installation

From the root of the repository, run the following commands:

1. **Clone the repository:**
   ```bash
   git clone https://github.com/your-username/mixmix-python-middleware.git
   cd mixmix-python-middleware
   ```

2. **Create a virtual environment:**
   ```bash
   python3 -m venv .venv
   ```

3. **Activate the virtual environment:**
   - On Linux/macOS:
     ```bash
     source .venv/bin/activate
     ```
   - On Windows:
     ```bash
     .venv\Scripts\activate
     ```

4. **Install the required dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

5. **Install the required dependencies for C++ integration:**
   ```bash
   apt-get update -y && apt-get install -y build-essential cmake libpng-dev libeigen3-dev libopencv-dev
   ```
   - Run with `sudo` if required.

6. **Build custom modules:**
   ```bash
   python setup.py install
   ```

7. **Copy the `.env.example` file to `.env`**
   ```bash
   cp .env.example .env
   ```
   - Fill in the required environment variables in the `.env` file.

## Deployment

The middleware is designed to be deployed using Docker. Build the Docker image using the following command:

```bash
docker build -t mixmix-python-middleware .
```

Run the Docker image locally using the following command:

```bash
docker run -d -p 8000:8000 mixmix-python-middleware
```

## Testing

Unit tests are available in the `tests` directory. To run the tests, use the following command:

```bash
pytest tests/
```

You can also run the tests using Docker by running the following command:

```bash
docker build -t mixmix-middleware .
docker run --rm mixmix-middleware python -m pytest tests/ -v
```
This will build the Docker image and run the tests inside the container.

## Usage

Once the Docker image is running, test the API endpoints using Swagger UI by accessing `http://localhost:8000/docs` in your browser.

## Structure

The repository is structured to support modularity and scalability. Below is the current structure:

```
mixmix-python-middleware/
├── app/
│   ├── api/        # API endpoints
│   ├── core/       # Core functions
│   ├── cpp/        # C++ scripts
│   ├── modal/      # Modal scripts
│   ├── services/   # All feature services provided
│   ├── utils/      # Utility functions
│   ├── __init__.py
│   ├── main.py     # Main FastAPI application
├── tests/          # Unit tests
├── .env.example    # Example environment variables
├── Dockerfile      # Dockerfile for building the image
├── README.md       # Project documentation
├── requirements.txt
├── setup.py        # Python setup file
```
