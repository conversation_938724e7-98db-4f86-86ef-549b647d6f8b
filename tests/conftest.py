import pytest
import os
import io
import shutil
from app.main import app
from fastapi.testclient import TestClient
from pathlib import Path

FIXTURE_DIR = Path(__file__).parent / "fixtures"
TEST_ARTWORK_DIR = Path("/tmp/test_artwork_png")
TEST_API_KEY = os.getenv("API_ACCESS_KEY", "api-key")


def pytest_addoption(parser):
    parser.addoption(
        "--env",
        action="store",
        default="local",
        help="Specify the environment: local or deployment",
    )


@pytest.fixture(scope="session")
def client(request):
    """Create a test client with API key authentication."""
    env = request.config.getoption("--env")

    # Create a test client
    if env == "local":
        test_client = TestClient(app, base_url="http://localhost:8000")
    elif env == "deployment":
        deployed_url = os.getenv("DEPLOYED_URL", "http://localhost:8000")
        test_client = TestClient(app, base_url=deployed_url)
    else:
        raise ValueError(f"Unknown environment: {env}")

    # Add API key to all requests by default
    original_request = test_client.request

    def request_with_auth(*args, **kwargs):
        # Add the API key header if not already present
        headers = kwargs.pop("headers", {}) or {}
        if "api-key" not in headers:
            headers["api-key"] = TEST_API_KEY
        kwargs["headers"] = headers
        return original_request(*args, **kwargs)

    # Replace the request method with our authenticated version
    test_client.request = request_with_auth

    yield test_client

    # Restore original request method when done
    test_client.request = original_request


@pytest.fixture(scope="session")
def input_jpg():
    """Provide pre-defined image files as bytes."""
    img = FIXTURE_DIR / "images" / "input.jpg"
    with open(img, "rb") as f:
        image_bytes = f.read()

    file = {
        "file": (
            "test_image.jpg",
            io.BytesIO(image_bytes),
            "image/jpeg",
        )
    }
    return file


@pytest.fixture(scope="session")
def input_png():
    """Provide pre-defined image files as bytes."""
    img = FIXTURE_DIR / "images" / "input.png"
    with open(img, "rb") as f:
        image_bytes = f.read()

    file = {
        "file": (
            "test_image.png",
            io.BytesIO(image_bytes),
            "image/png",
        )
    }
    return file


@pytest.fixture(scope="session", autouse=True)
def setup_test_artwork_dir():
    """Set up test artwork directory for the entire test session."""
    os.makedirs(TEST_ARTWORK_DIR, exist_ok=True)
    yield
    shutil.rmtree(TEST_ARTWORK_DIR, ignore_errors=True)


@pytest.fixture(autouse=True)
def patch_artwork_dir(monkeypatch):
    """Patch ARTWORK_DIR to use test directory for all tests."""
    monkeypatch.setattr("app.utils.file_utils.ARTWORK_DIR", str(TEST_ARTWORK_DIR))
    # Also patch the file_uploads module since it imports ARTWORK_DIR directly
    monkeypatch.setattr("app.api.endpoints.file_uploads.ARTWORK_DIR", str(TEST_ARTWORK_DIR))


@pytest.fixture
def upload_test_image(client):
    """Upload a test image and return the filename for use in tests."""
    img_path = FIXTURE_DIR / "images" / "input.png"

    with open(img_path, "rb") as f:
        response = client.post(
            "/files/upload-artwork/", files={"file": ("input.png", f, "image/png")}
        )

    assert response.status_code == 201
    return "input.png"
