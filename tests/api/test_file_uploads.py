import pytest
import os
import shutil
from pathlib import Path

from fastapi.testclient import Test<PERSON>lient
from app.main import app  # Assuming your FastAPI app instance is named 'app' in main.py

# Directory for storing test artwork, relative to this test file
# This should match the ARTWORK_DIR in your actual endpoint, or be configurable for tests
TEST_ARTWORK_DIR = Path("/tmp/test_artwork_png")  # Using a temporary directory for tests
ACTUAL_ARTWORK_DIR_IN_ENDPOINT = "/artwork/png"  # The actual path used in the endpoint


@pytest.fixture(scope="module", autouse=True)
def setup_test_artwork_dir():
    os.makedirs(TEST_ARTWORK_DIR, exist_ok=True)
    # This is a trick: if your endpoint hardcodes "/artwork/png",
    # we might need to mock `os.path.join` or the `ARTWORK_DIR` constant
    # For now, let's assume we can test the logic even if the path is different locally,
    # or that the endpoint's ARTWORK_DIR can be configured for tests.
    # A better approach would be to make ARTWORK_DIR configurable in the app.
    yield
    shutil.rmtree(TEST_ARTWORK_DIR)


@pytest.fixture
def png_file_from_fixtures():
    fixture_file_path = Path("tests/fixtures/images/input.png")
    filename = fixture_file_path.name
    return fixture_file_path, filename


# Monkeypatch the ARTWORK_DIR in the file_uploads module for testing
@pytest.fixture(autouse=True)
def patch_artwork_dir(monkeypatch):
    monkeypatch.setattr("app.api.endpoints.file_uploads.ARTWORK_DIR", str(TEST_ARTWORK_DIR))


def test_upload_png_file(client, png_file_from_fixtures):
    file_path, filename = png_file_from_fixtures

    with open(file_path, "rb") as f:
        response = client.post("/files/upload-artwork/", files={"file": (filename, f, "image/png")})

    assert response.status_code == 201
    json_response = response.json()
    assert (
        json_response["message"]
        == f"File '{filename}' uploaded successfully to {TEST_ARTWORK_DIR / filename}"
    )

    # Check if file exists in the (mocked) artwork directory
    assert (TEST_ARTWORK_DIR / filename).exists()
    assert (TEST_ARTWORK_DIR / filename).is_file()


def test_delete_existing_png_file(client, png_file_from_fixtures):
    # First, upload a file to ensure it exists
    file_path, filename = png_file_from_fixtures
    with open(file_path, "rb") as f:
        upload_response = client.post(
            "/files/upload-artwork/", files={"file": (filename, f, "image/png")}
        )
    assert upload_response.status_code == 201
    assert (TEST_ARTWORK_DIR / filename).exists()

    # Now, delete the file
    delete_response = client.delete(f"/files/delete-artwork/{filename}")

    assert delete_response.status_code == 200
    json_response = delete_response.json()
    assert json_response["message"] == f"File '{filename}' deleted successfully."

    # Check if file no longer exists
    assert not (TEST_ARTWORK_DIR / filename).exists()


def test_delete_non_existing_file(client):
    non_existent_filename = "does_not_exist.png"
    response = client.delete(f"/files/delete-artwork/{non_existent_filename}")

    assert response.status_code == 404
    json_response = response.json()
    assert json_response["detail"] == f"File '{non_existent_filename}' not found."
