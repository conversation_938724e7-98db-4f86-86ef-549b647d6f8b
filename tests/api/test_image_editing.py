import pytest
import io
import json
from fastapi.testclient import TestClient
from PIL import Image


class TestImageEditingEndpoint:
    @pytest.mark.parametrize(
        "endpoint, output_jpg_path",
        [
            ("/adjust-hue-saturation", "tests/fixtures/outputs/hue_sat-0.2.jpg"),
            ("/convert-to-grayscale", "tests/fixtures/outputs/grayscale.jpg"),
            ("/invert-colors", "tests/fixtures/outputs/invert.jpg"),
        ],
    )
    def test_output_jpg_is_correct(self, client: TestClient, input_jpg, output_jpg_path, endpoint):
        """Test that hue-saturation adjustment works for JPG format."""
        # Make the API request with endpoint check
        if endpoint == "/adjust-hue-saturation":
            params = {"hue": 0.2, "saturation": 0.2}
            response = client.post(
                endpoint,
                files=input_jpg,
                params=params,
            )
        else:
            # Skip params for other endpoints that don't need hue/saturation
            response = client.post(
                endpoint,
                files=input_jpg,
            )

        assert response.status_code == 200, f"Error: {response.text}"

        assert response.headers["Content-Type"] in [
            "image/jpg",
            "image/jpeg",
        ]

        processed_image_bytes = response.content
        assert len(processed_image_bytes) > 0, "Processed image should not be empty"

        # Try to open the image to confirm it's valid
        try:
            image = Image.open(io.BytesIO(processed_image_bytes))
            assert image.mode == "RGB", f"Unexpected image mode: {image.mode}"

            original_image = Image.open(io.BytesIO(input_jpg["file"][1].getvalue()))
            assert image.size == original_image.size, "Image dimensions changed"

            with open(output_jpg_path, "rb") as f:
                expected_output = Image.open(io.BytesIO(f.read()))

            processed_histogram = image.histogram()
            expected_histogram = expected_output.histogram()

            # Calculate histogram similarity
            similarity = sum(
                min(a, b) for a, b in zip(processed_histogram, expected_histogram)
            ) / sum(expected_histogram)
            assert (
                similarity == 1.0
            ), f"Processed image doesn't match expected output (similarity: {similarity})"

        except Exception as e:
            pytest.fail(f"Failed to process the returned image: {e}")

    @pytest.mark.parametrize("endpoint", ["/extract-colors"])
    def test_colors_are_extractable(self, client: TestClient, input_jpg, endpoint):
        """Test that colors can be extracted from an image."""
        response = client.post(endpoint, files=input_jpg)
        assert response.status_code == 200, f"Error: {response.text}"

        colors = response.json()
        assert isinstance(colors, list), "Colors should be returned as a list"
        assert len(colors) > 0, "At least one color should be extracted"

    @pytest.mark.parametrize(
        "texturize, output_colors",
        [(1, 132391), (256, 1), (30, 131860)],
    )
    def test_replace_colors(self, client: TestClient, input_png, texturize, output_colors):
        """Test that colors can be replaced in an image."""
        color_to_replace = "#002E3D"
        new_color = "#000000"

        replacements_json = json.dumps({color_to_replace: new_color})
        form_data = {
            "replacements": replacements_json,
            "texturize": str(texturize),
        }
        response = client.post(
            "/replace-colors",
            files=input_png,
            data=form_data,
        )
        assert response.status_code == 200, f"Error: {response.text}"

        processed_image_bytes = response.content
        assert len(processed_image_bytes) > 0, "Processed image should not be empty"

        # Try to open the image to confirm it's valid
        try:
            image = Image.open(io.BytesIO(processed_image_bytes))
            assert image.mode == "RGB", f"Unexpected image mode: {image.mode}"

            # Check the number of unique colors in the processed image
            unique_colors = image.getcolors(maxcolors=16777216)
            assert (
                len(unique_colors) == output_colors
            ), f"Unexpected number of unique colors: {len(unique_colors)}"

        except Exception as e:
            pytest.fail(f"Failed to process the returned image: {e}")

    def test_empty_color_replacement(self, client: TestClient, input_png):
        """Test behavior when an empty replacements dictionary is provided."""
        import numpy as np

        response = client.post(
            "/replace-colors",
            files=input_png,
            data={"replacements": "{}", "texturize": "256"},
        )

        assert response.status_code == 200, f"Error: {response.text}"

        # Image should be unchanged
        original_img = Image.open(io.BytesIO(input_png["file"][1].getvalue()))
        processed_img = Image.open(io.BytesIO(response.content))

        # Convert to arrays for comparison
        original_array = np.array(original_img)
        processed_array = np.array(processed_img)

        assert np.array_equal(
            original_array, processed_array
        ), "Image should be unchanged with empty replacements"
