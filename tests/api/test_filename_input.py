import os
import shutil
import pytest
from pathlib import Path
from fastapi.testclient import TestClient
from app.main import app

# Use a known PNG test image from fixtures
TEST_IMAGE_SRC = os.path.join(os.path.dirname(__file__), "../fixtures/images/input.png")
API_KEY = os.environ.get("API_ACCESS_KEY", "test")


@pytest.fixture(scope="module", autouse=True)
def setup_test_image():
    # Use the global TEST_ARTWORK_DIR which is set up by conftest.py
    TEST_ARTWORK_DIR = Path("/tmp/test_artwork_png")
    os.makedirs(TEST_ARTWORK_DIR, exist_ok=True)
    test_image_dst = TEST_ARTWORK_DIR / "test_filename_input.png"
    shutil.copy(TEST_IMAGE_SRC, test_image_dst)
    yield


client = TestClient(app)


def test_limit_colors_with_filename():
    response = client.post(
        "/limit-colors",
        data={"filename": "test_filename_input.png", "num_colors": 4, "algorithm": "median_cut"},
        headers={"api-key": API_KEY},
    )
    assert (
        response.status_code == 200
    ), f"Unexpected status: {response.status_code}, body: {response.text}"
    assert response.headers["content-type"].startswith("image/")
    assert response.content  # Should not be empty


def test_list_artwork_files():
    response = client.get(
        "/files/list-artwork/",
        headers={"api-key": API_KEY},
    )
    assert (
        response.status_code == 200
    ), f"Unexpected status: {response.status_code}, body: {response.text}"
    data = response.json()
    assert "files" in data, "Response should contain a 'files' key"
    assert isinstance(data["files"], list), "'files' should be a list"
