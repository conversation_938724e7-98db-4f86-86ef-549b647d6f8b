import pytest
import io
from fastapi.testclient import TestClient
from PIL import Image
import numpy as np


class TestImageEditingEndpoint:
    endpoint = "/limit-colors"

    @pytest.mark.parametrize(
        "output_img_path, num_colors, algorithm",
        [
            ("tests/fixtures/outputs/k_means_6.png", 6, "k_means"),
            ("tests/fixtures/outputs/median_cut_6.png", 6, "median_cut"),
            ("tests/fixtures/outputs/adaptive_dithering_12.png", 12, "adaptive_dithering"),
        ],
    )
    def test_output_png_is_correct(
        self, client: TestClient, input_png, output_img_path, num_colors, algorithm
    ):
        """Test that hue-saturation adjustment works for PNG format."""
        response = client.post(
            self.endpoint,
            files=input_png,
            params={"num_colors": num_colors, "algorithm": algorithm},
        )

        assert response.status_code == 200, f"Error: {response.text}"
        assert response.headers["Content-Type"] == "image/png"

        processed_image_bytes = response.content
        assert len(processed_image_bytes) > 0, "Processed image should not be empty"

        # Try to open the image to confirm it's valid
        try:
            processed_image = Image.open(io.BytesIO(processed_image_bytes))
            processed_image = processed_image.convert(
                "RGB"
            )  # Convert to RGB mode to ensure consistency
            assert (
                processed_image.mode == "RGB"
            ), f"Unexpected processed_image mode: {processed_image.mode}"

            original_image = Image.open(io.BytesIO(input_png["file"][1].getvalue()))
            assert processed_image.size == original_image.size, "Image dimensions changed"

            # Verify processed_image quality with structural similarity index
            from skimage.metrics import structural_similarity as ssim

            # Convert to grayscale for simpler comparison
            original_gray = original_image.convert("L")
            processed_gray = processed_image.convert("L")

            original_array = np.array(original_gray)
            processed_array = np.array(processed_gray)

            similarity = ssim(original_array, processed_array)
            print(f"Structural similarity: {similarity:.4f}")

            # Images should still be somewhat similar to the original
            assert similarity > 0.5, f"Image quality too low (similarity: {similarity})"

        except Exception as e:
            pytest.fail(f"Failed to process the returned image: {e}")

    @pytest.mark.parametrize(
        "num_colors",
        [
            1,
            12,
            256,
        ],
    )
    def test_jpg_is_returned(self, client: TestClient, input_jpg, num_colors):
        """Test that the endpoint returns a JPG image."""
        response = client.post(
            self.endpoint,
            files=input_jpg,
            params={"num_colors": num_colors},
        )

        assert response.status_code == 200, f"Error: {response.text}"
        assert response.headers["Content-Type"] in [
            "image/jpg",
            "image/jpeg",
        ]

        processed_image_bytes = response.content
        assert len(processed_image_bytes) > 0, "Processed image should not be empty"

        # Try to open the image to confirm it's valid
        try:
            image = Image.open(io.BytesIO(processed_image_bytes))
            assert image.mode == "RGB", f"Unexpected image mode: {image.mode}"

        except Exception as e:
            pytest.fail(f"Failed to process the returned image: {e}")
