import pytest
import io
from fastapi import UploadFile
from fastapi.testclient import TestClient
from PIL import Image


class TestImageEditingEndpoint:
    @pytest.mark.parametrize("endpoint", ["/search"])
    def test_search(self, client: TestClient, input_jpg, endpoint):
        """Test that search returns results from an image."""
        num_results = 5
        response = client.post(endpoint, params={"num_results": num_results}, files=input_jpg)
        assert response.status_code == 200, f"Error: {response.text}"

        results = response.json()
        assert isinstance(results, list), "Results should be returned as a list"
        assert len(results) == num_results, f"Exactly {num_results} results should be returned"
        for result in results:
            assert isinstance(result, dict), "Each result should be a dictionary"
            assert "image_name" in result, "Each result should have an image key"
            assert "score" in result, "Each result should have a score key"
