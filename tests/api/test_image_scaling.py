import pytest
import io
import os
import numpy as np
from fastapi.testclient import Test<PERSON>lient
from PIL import Image


class TestImageScalingEndpoint:
    endpoint = "/scale-image"

    def test_identity_scaling_preserves_content(
        self,
        client: TestClient,
        input_png,
        scale_factor=1.0,
    ):
        """Test that scaling with factor 1.0 returns the identical image"""
        # Send API request
        response = client.post(
            self.endpoint,
            files=input_png,
            data={"scale_factor": scale_factor},
        )

        # Check status code
        assert response.status_code == 200, f"Error: {response.text}"

        # Load both images and compare pixels
        with Image.open(input_png["file"][1]) as orig_img:
            with Image.open(io.BytesIO(response.content)) as result_img:
                # Convert to same format for comparison
                if orig_img.mode != result_img.mode:
                    orig_img = orig_img.convert(result_img.mode)

                orig_array = np.array(orig_img)
                result_array = np.array(result_img)

                # Compare
                assert np.array_equal(
                    orig_array, result_array
                ), "Content changed after identity scaling"

    @pytest.mark.parametrize(
        "scale_factor",
        [0.6, 1.35],
    )
    def test_correct_dimensions_after_scaling(
        self,
        client: TestClient,
        input_png,
        scale_factor,
    ):
        """Test that the dimensions of the image are correctly scaled"""
        # Send API request
        response = client.post(
            self.endpoint,
            files=input_png,
            data={"scale_factor": scale_factor},
        )

        # Check status code
        assert response.status_code == 200, f"Error: {response.text}"

        # Load original image to get dimensions
        with Image.open(input_png["file"][1]) as orig_img:
            original_width, original_height = orig_img.size

        # Calculate expected dimensions
        expected_width = int(original_width * scale_factor)
        expected_height = int(original_height * scale_factor)

        # Load result image and check dimensions
        with Image.open(io.BytesIO(response.content)) as result_img:
            result_width, result_height = result_img.size

            assert (
                result_width == expected_width
            ), f"Width is incorrect, expected {expected_width}, got {result_width}"
            assert (
                result_height == expected_height
            ), f"Height is incorrect, expected {expected_height}, got {result_height}"

    @pytest.mark.parametrize(
        "scale_factor",
        [(sf) for sf in [-0.5, 0.0003, 15.1]],
    )
    def test_invalid_scale_factors(
        self,
        client: TestClient,
        input_jpg,
        scale_factor,
    ):
        """Test that invalid scale factors are properly handled"""
        response = client.post(
            self.endpoint,
            files=input_jpg,
            data={"scale_factor": scale_factor},
        )

        # Should return an error for invalid scale factors
        assert (
            response.status_code == 422
        ), f"Expected error for invalid scale factor {scale_factor}"

    def test_default_scale_factor(
        self,
        client: TestClient,
        input_png,
    ):
        """Test that omitting scale_factor uses the default value of 1.0"""
        # Send API request without scale_factor (should use default)
        response = client.post(
            self.endpoint,
            files=input_png,
            # No scale_factor specified - should use default
        )

        # Check status code
        assert response.status_code == 200, f"Error: {response.text}"

        # Load both images and compare pixels (should be identical with scale_factor=1.0)
        with Image.open(input_png["file"][1]) as orig_img:
            with Image.open(io.BytesIO(response.content)) as result_img:
                # Convert to same format for comparison
                if orig_img.mode != result_img.mode:
                    orig_img = orig_img.convert(result_img.mode)

                orig_array = np.array(orig_img)
                result_array = np.array(result_img)

                # Compare
                assert np.array_equal(
                    orig_array, result_array
                ), "Content changed with default scale factor"
