import pytest
import io
import zipfile
from pathlib import Path
from PIL import Image
from .base import ImageUploadTestBase


def pipeline_body(operations, tech_pack=False):
    return {"operations": operations, "tech_pack": tech_pack}


class TestGenerateFinalOutput(ImageUploadTestBase):
    @pytest.fixture(autouse=True)
    def setup(self, client, upload_test_image):
        self.client = client
        self.filename = upload_test_image

    def test_limit_colors_pipeline(self):
        body = pipeline_body([
            {"endpoint_name": "limit-colors", "params": {"num_colors": 6, "algorithm": "k_means"}}
        ])
        response = self.client.post(f"/generate-final-output/{self.filename}", json=body)
        self.assert_png_response(response)
        # Optionally, check image properties
        img = Image.open(io.BytesIO(response.content))
        assert img.format == "PNG"

    def test_replace_colors_pipeline(self):
        body = pipeline_body([
            {"endpoint_name": "replace-colors", "params": {"replacements": {"root": {"#002E3D": "#000000"}}, "texturize": 1}}
        ])
        response = self.client.post(f"/generate-final-output/{self.filename}", json=body)
        self.assert_png_response(response)

    def test_full_pipeline(self):
        body = pipeline_body([
            {"endpoint_name": "limit-colors", "params": {"num_colors": 8, "algorithm": "k_means"}},
            {"endpoint_name": "replace-colors", "params": {"replacements": {"root": {"#C0C0C0": "#FFD700"}}, "texturize": 3}},
            {"endpoint_name": "convert-to-grayscale", "params": {}}
        ])
        response = self.client.post(f"/generate-final-output/{self.filename}", json=body)
        self.assert_png_response(response)

    def test_invalid_endpoint(self):
        body = pipeline_body([
            {"endpoint_name": "not-a-real-op", "params": {}}
        ])
        response = self.client.post(f"/generate-final-output/{self.filename}", json=body)
        assert response.status_code == 400
        assert "Unknown endpoint_name" in response.text

    def test_missing_required_param(self):
        body = pipeline_body([
            {"endpoint_name": "replace-colors", "params": {}}
        ])
        response = self.client.post(f"/generate-final-output/{self.filename}", json=body)
        assert response.status_code == 400
        assert "replacements.root" in response.text

    def test_extract_colors_noop(self):
        body = pipeline_body([
            {"endpoint_name": "extract-colors", "params": {}}
        ])
        response = self.client.post(f"/generate-final-output/{self.filename}", json=body)
        self.assert_png_response(response)

    def test_tech_pack_generation(self):
        """Test that tech_pack=True generates a zip file with PSD, artwork PDF, and 11x17 PDF."""
        body = pipeline_body([
            {"endpoint_name": "limit-colors", "params": {"num_colors": 8, "algorithm": "k_means"}}
        ], tech_pack=True)

        response = self.client.post(f"/generate-final-output/{self.filename}", json=body)
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/zip"

        # Check that the response contains a valid zip file
        zip_content = io.BytesIO(response.content)
        with zipfile.ZipFile(zip_content, 'r') as zip_file:
            file_names = zip_file.namelist()

            # Check that all expected files are present
            base_name = Path(self.filename).stem
            expected_files = [
                f"{base_name}.psd",
                f"{base_name}_artwork_info.pdf",
                f"{base_name}_11x17.pdf"
            ]

            for expected_file in expected_files:
                assert expected_file in file_names, f"Expected file {expected_file} not found in zip"

            # Verify that files are not empty
            for file_name in expected_files:
                file_info = zip_file.getinfo(file_name)
                assert file_info.file_size > 0, f"File {file_name} is empty"

    def test_tech_pack_false_returns_png(self):
        """Test that tech_pack=False returns a PNG file (default behavior)."""
        body = pipeline_body([
            {"endpoint_name": "limit-colors", "params": {"num_colors": 8, "algorithm": "k_means"}}
        ], tech_pack=False)

        response = self.client.post(f"/generate-final-output/{self.filename}", json=body)
        self.assert_png_response(response)

    def test_tech_pack_default_false(self):
        """Test that omitting tech_pack defaults to False and returns PNG."""
        body = {"operations": [
            {"endpoint_name": "limit-colors", "params": {"num_colors": 8, "algorithm": "k_means"}}
        ]}

        response = self.client.post(f"/generate-final-output/{self.filename}", json=body)
        self.assert_png_response(response)

    def test_tech_pack_with_direct_file(self):
        """Test tech pack generation by directly placing a test file in the artwork directory."""
        # Copy test image directly to the artwork directory to bypass upload issues
        import shutil
        from pathlib import Path

        test_img_path = Path("tests/fixtures/images/input.png")
        artwork_dir = Path("/tmp/test_artwork_png")
        artwork_dir.mkdir(exist_ok=True)

        # Copy test image to artwork directory
        target_path = artwork_dir / "direct_test.png"
        shutil.copy(test_img_path, target_path)

        # Test tech pack generation
        body = pipeline_body([
            {"endpoint_name": "limit-colors", "params": {"num_colors": 6, "algorithm": "k_means"}}
        ], tech_pack=True)

        response = self.client.post("/generate-final-output/direct_test.png", json=body)
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/zip"

        # Verify zip contents
        zip_content = io.BytesIO(response.content)
        with zipfile.ZipFile(zip_content, 'r') as zip_file:
            file_names = zip_file.namelist()
            expected_files = ["direct_test.psd", "direct_test_artwork_info.pdf", "direct_test_11x17.pdf"]

            for expected_file in expected_files:
                assert expected_file in file_names, f"Expected file {expected_file} not found in zip"

            # Verify files are not empty
            for file_name in expected_files:
                file_info = zip_file.getinfo(file_name)
                assert file_info.file_size > 0, f"File {file_name} is empty"


class TestTechPackFunctionality:
    """Standalone tests for tech pack functionality that don't require file upload."""

    @pytest.fixture(autouse=True)
    def setup_direct_test(self, client):
        """Setup test without requiring file upload."""
        self.client = client
        # Setup test file directly
        import shutil
        from pathlib import Path

        test_img_path = Path("tests/fixtures/images/input.png")
        artwork_dir = Path("/tmp/test_artwork_png")
        artwork_dir.mkdir(exist_ok=True)

        # Copy test image to artwork directory
        self.test_filename = "tech_pack_test.png"
        target_path = artwork_dir / self.test_filename
        shutil.copy(test_img_path, target_path)

    def test_tech_pack_creation(self):
        """Test that tech_pack=True generates a valid zip with all required files."""
        body = {
            "operations": [
                {"endpoint_name": "limit-colors", "params": {"num_colors": 6, "algorithm": "k_means"}}
            ],
            "tech_pack": True
        }

        response = self.client.post(f"/generate-final-output/{self.test_filename}", json=body)
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/zip"

        # Verify zip contents
        zip_content = io.BytesIO(response.content)
        with zipfile.ZipFile(zip_content, 'r') as zip_file:
            file_names = zip_file.namelist()
            base_name = "tech_pack_test"
            expected_files = [
                f"{base_name}.psd",
                f"{base_name}_artwork_info.pdf",
                f"{base_name}_11x17.pdf"
            ]

            for expected_file in expected_files:
                assert expected_file in file_names, f"Expected file {expected_file} not found in zip"

            # Verify files are not empty
            for file_name in expected_files:
                file_info = zip_file.getinfo(file_name)
                assert file_info.file_size > 0, f"File {file_name} is empty"

        print(f"Tech pack zip created successfully with {len(file_names)} files")

    def test_tech_pack_false_behavior(self):
        """Test that tech_pack=False returns PNG as usual."""
        body = {
            "operations": [
                {"endpoint_name": "limit-colors", "params": {"num_colors": 6, "algorithm": "k_means"}}
            ],
            "tech_pack": False
        }

        response = self.client.post(f"/generate-final-output/{self.test_filename}", json=body)
        assert response.status_code == 200
        assert response.headers["content-type"] == "image/png"
        assert len(response.content) > 0
        # Basic PNG signature check
        assert response.content[:8] == b'\x89PNG\r\n\x1a\n'
