import os


class APITestClass:
    __api_key = os.getenv("API_ACCESS_KEY")
    endpoint = None

    @property
    def api_key(self):
        return self.__api_key


class ImageUploadTestBase:
    """Base class for tests that need to upload and process images."""

    def assert_png_response(self, response):
        """Assert that the response is a valid PNG file."""
        assert response.status_code == 200
        assert response.headers["content-type"] == "image/png"
        assert len(response.content) > 0
        # Basic PNG signature check
        assert response.content[:8] == b"\x89PNG\r\n\x1a\n"


class ImageProcessorTest:
    def test_jpg(self):
        pass

    def test_png(self):
        pass
