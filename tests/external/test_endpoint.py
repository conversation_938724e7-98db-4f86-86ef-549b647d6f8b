import requests
from app.utils.logging import log_time
from dotenv import load_dotenv
import os


@log_time
def get_cloth(cloth_path, api_key):
    MODAL_URL = "https://developer--cloth-detection-objectdetection-run-analysis-dev.modal.run"

    headers = {
        "Authorization": f"Bearer {api_key}",
    }
    files = {"file": open(cloth_path, "rb")}

    response = requests.post(MODAL_URL, headers=headers, files=files)

    # save the image to a file
    with open("app/images/modal_cloth.jpg", "wb") as f:
        f.write(response.content)


if __name__ == "__main__":
    load_dotenv()
    cloth_path = "app/images/test_6.jpg"
    # cloth_path = 'tests/images/SOHR084.jpg'
    # cloth_path = 'tests/images/MMMP885_P.jpg'
    api_key = os.getenv("MODAL_KEY")
    get_cloth(cloth_path, api_key)
