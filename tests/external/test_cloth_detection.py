import modal
import time
import numpy as np
from PIL import Image
from app.utils.logging import log_time


@log_time
def _test_cloth_detection():
    start = time.time()
    cls = modal.Cls.lookup("cloth-detection", "ObjectDetection")
    detector = cls()
    print("Model loaded in", time.time() - start, "seconds")
    image = Image.open("app/images/test_7.jpg")

    start = time.time()
    result = detector.run_analysis.remote(image)
    print("Cloth detected in", time.time() - start, "seconds")
    assert result is not None

    start = time.time()
    cloth_image = Image.fromarray(result.astype(np.uint8))
    cloth_image.save("app/images/modal_cloth.jpg")
    print("Cloth saved in", time.time() - start, "seconds")


if __name__ == "__main__":
    _test_cloth_detection()
