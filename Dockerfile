FROM python:3.9-slim

# Set environment variables for Python
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    TRANSFORMERS_CACHE=/data/transformers_cache

# Install system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        build-essential \
        python3-dev \
        libopencv-dev \
        pkg-config && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Create and set working directory
WORKDIR /app

# Copy project files into container
COPY . /app

# Upgrade pip and install Python dependencies
RUN cd /app && \
    pip install --upgrade pip && \
    pip install -r requirements.txt

# Build the C++ extension module for Python and install the project
RUN pip install . && \
    rm -rf build *.egg-info dist

# Expose port for FastAPI application
EXPOSE 8000

# Run FastAPI application with uvicorn
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
